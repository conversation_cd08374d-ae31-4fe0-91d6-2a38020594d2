<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '微信信息',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="wechat-info-container">
    <!-- 导航栏 -->
    <Navbar
      title="微信信息"
      :fixed="true"
      :placeholder="true"
      custom-class="custom-navbar"
      @back="handleBack"
    />

    <!-- 内容区域 -->
    <view class="page-content">
      <view class="main-layout">
        <!-- 微信信息填写区域 -->
        <view class="wechat-info-section">
          <!-- 头像选择 -->
          <view class="avatar-area">
            <wd-button
              class="avatar-btn"
              open-type="chooseAvatar"
              @chooseavatar="onChooseAvatar"
              custom-style="background: transparent; border: none; padding: 0; width: 100px; height: 100px;"
            >
              <view class="avatar-container">
                <wd-img
                  v-if="selectedAvatar"
                  :src="selectedAvatar"
                  class="avatar-image"
                  mode="aspectFill"
                  width="100px"
                  height="110px"
                  round
                />
                <view v-else class="avatar-placeholder">
                  <text class="iconfont-sys iconsys-camera"></text>
                </view>
              </view>
            </wd-button>
          </view>

          <!-- 昵称输入 -->
          <view class="nickname-area">
            <view class="nickname-label">昵称</view>
            <view class="nickname-input-wrapper">
              <wd-input
                v-model="nickname"
                type="nickname"
                placeholder="请输入昵称"
                :maxlength="20"
                custom-class="nickname-input"
                @input="onNicknameInput"
              />
              <view class="input-counter">{{ nickname.length }}/20</view>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-section">
          <wd-button
            size="large"
            custom-class="cancel-btn"
            custom-style="flex: 1; height: 48px; border-radius: 24px; background: white; color: #666; border: 1px solid #e0e0e0; margin-right: 12px;"
            @click="handleCancel"
          >
            取消
          </wd-button>
          <wd-button
            type="primary"
            size="large"
            :disabled="!canConfirm"
            custom-class="confirm-btn"
            custom-style="flex: 1; height: 48px; border-radius: 24px;"
            @click="handleConfirm"
          >
            确认绑定
          </wd-button>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import WxUtil from '@/utils/wxUtil'

// 页面参数
const action = ref<'bind' | 'update'>('bind')

// 状态管理
const selectedAvatar = ref('')
const nickname = ref('')

// 计算属性
const canConfirm = computed(() => {
  return selectedAvatar.value && nickname.value.trim().length > 0
})

// 页面加载
onLoad((options) => {
  if (options?.action) {
    action.value = options.action as 'bind' | 'update'
  }
})

// 选择头像
const onChooseAvatar = (e: any) => {
  console.log('头像选择事件:', e)
  const avatarUrl = e.detail?.avatarUrl || e.detail?.avatar || e.avatarUrl
  if (avatarUrl) {
    console.log('用户选择头像:', avatarUrl)
    selectedAvatar.value = avatarUrl
  } else {
    console.error('未能获取到头像URL:', e)
    uni.showToast({
      title: '头像选择失败',
      icon: 'none',
    })
  }
}

// 昵称输入
const onNicknameInput = (e: any) => {
  console.log('昵称输入事件:', e)
  if (e && e.detail && typeof e.detail.value !== 'undefined') {
    nickname.value = e.detail.value
  } else if (e && typeof e === 'string') {
    // 处理直接传入字符串的情况
    nickname.value = e
  } else {
    console.warn('昵称输入事件格式异常:', e)
  }
}

// 确认操作
const handleConfirm = async () => {
  if (!canConfirm.value) {
    uni.showToast({
      title: '请完善信息',
      icon: 'none',
    })
    return
  }

  try {
    // 在确认绑定时获取微信授权码，保证code的时效性
    console.log('开始获取微信授权码')
    const wxLoginResult = await WxUtil.login()
    console.log('获取到微信登录code:', wxLoginResult.code)

    const eventChannel = (uni as any).getOpenerEventChannel()
    if (eventChannel) {
      // 向上一页传递数据，包含code
      eventChannel.emit('acceptDataFromWechatInfo', {
        nickname: WxUtil.formatNickname(nickname.value),
        avatar: selectedAvatar.value,
        code: wxLoginResult.code, // 传递最新的授权码
      })
    }

    // 返回到仪表板
    uni.redirectTo({
      url: '/pages-sys/dashboard/index',
    })
  } catch (error) {
    console.error('获取微信授权码失败:', error)
    uni.showToast({
      title: '获取微信授权失败',
      icon: 'none',
    })
  }
}

// 取消操作 - 返回到仪表板
const handleCancel = () => {
  uni.redirectTo({
    url: '/pages-sys/dashboard/index',
  })
}

// 返回处理 - 返回到仪表板
const handleBack = () => {
  console.log('点击返回按钮')
  uni.redirectTo({
    url: '/pages-sys/dashboard/index',
  })
}
</script>

<style lang="scss" scoped>
.wechat-info-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: white;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #07c160 !important;

  .wd-navbar__title {
    color: white !important;
    font-size: 20px !important;
    font-weight: bold !important;
  }

  .wd-navbar__left .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;

    .wd-icon {
      font-size: 26px !important;
      color: white !important;
    }
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.main-layout {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// 微信信息填写区域
.wechat-info-section {
  padding: 40px 24px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

// 头像区域
.avatar-area {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.avatar-container {
  width: 100px;
  height: 100px;
  border: 2px dashed #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: transparent;
}

// 头像按钮样式
:deep(.avatar-btn) {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

// 昵称区域
.nickname-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nickname-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  min-width: 40px;
}

:deep(.avatar-btn) {
  overflow: hidden;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

// 头像图片样式
:deep(.avatar-image) {
  width: 100px !important;
  height: 110px !important;
  border-radius: 50% !important;
  overflow: hidden !important;

  // 小程序端兼容
  .wd-img__image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    border-radius: 50% !important;
  }
}

.avatar-placeholder {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;

  .iconfont-sys {
    font-size: 32px;
    color: #999;
  }
}

.nickname-input-wrapper {
  position: relative;
  flex: 1;
}

:deep(.nickname-input) {
  .wd-input {
    width: 100%;
    height: 48px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0 16px;
    font-size: 16px;
    background: white;

    &:focus {
      border-color: #07c160;
      outline: none;
    }
  }
}

.input-counter {
  position: absolute;
  right: 12px;
  bottom: -24px;
  font-size: 12px;
  color: #999;
}

// 操作按钮区域
.action-section {
  display: flex;
  flex-direction: row;
  gap: 0;
  margin-top: 20px;
}
</style>
