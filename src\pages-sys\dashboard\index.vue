<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '仪表盘',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="dashboard-container" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      title="仪表盘"
      fixed
      placeholder
      safe-area-inset-top
      @click-left="openSideMenu"
      custom-class="custom-navbar"
    >
      <template #left>
        <wd-icon name="format-vertical-align-left" custom-class="menu-icon" />
      </template>
    </wd-navbar>

    <view class="page-content">
      <view class="content-area">
        <view class="content-placeholder">
          <text class="placeholder-text">仪表盘内容区域</text>
        </view>
      </view>
    </view>

    <!-- 底部系统TabBar组件 -->
    <SysTabBar :current-page="'dashboard'" />

    <!-- 左侧菜单弹窗 -->
    <wd-popup
      v-model="showSideMenu"
      position="left"
      custom-style="width: 280px; height: 100vh;"
      :safe-area-inset-bottom="true"
      @close="closeSideMenu"
      :z-index="1000"
    >
      <view class="side-menu-container">
        <!-- 用户信息区域 - 增加顶部间距避免被navbar遮盖 -->
        <view class="user-info-section" :style="{ paddingTop: safeAreaInsetsTop + 60 + 'px' }">
          <view class="user-avatar-large">
            <!-- 如果有头像则显示头像，否则显示默认图标 -->
            <image
              v-if="systemAdminStore.adminInfo?.avatar"
              :src="systemAdminStore.adminInfo.avatar"
              class="user-avatar-image"
              mode="aspectFill"
            />
            <wd-icon v-else name="user-circle" size="60" color="#4285f4" />
          </view>
          <view class="user-info">
            <text class="user-name">
              {{
                systemAdminStore.adminInfo?.real_name ||
                systemAdminStore.adminInfo?.username ||
                '未知用户'
              }}
            </text>
            <text class="user-role">
              {{ systemAdminStore.adminInfo?.roles[0]?.name || '系统用户' }}
            </text>
          </view>
        </view>

        <!-- 菜单选项 -->
        <view class="menu-section">
          <view class="menu-group">
            <text class="group-title">个人设置</text>
            <view class="menu-item" @click="goToProfile">
              <wd-icon name="setting" size="20" color="#666" />
              <text class="menu-text">个人信息</text>
              <wd-icon name="arrow-right" size="16" color="#ccc" />
            </view>
            <view class="menu-item" @click="goToChangePassword">
              <wd-icon name="lock-on" size="20" color="#666" />
              <text class="menu-text">修改密码</text>
              <wd-icon name="arrow-right" size="16" color="#ccc" />
            </view>
          </view>

          <view class="menu-group">
            <text class="group-title">其他</text>
            <view class="menu-item" @click="goToAbout">
              <wd-icon name="info-circle" size="20" color="#666" />
              <text class="menu-text">关于系统</text>
              <wd-icon name="arrow-right" size="16" color="#ccc" />
            </view>
            <view class="menu-item logout-item" @click="handleLogout">
              <wd-icon name="logout" size="20" color="#f44336" />
              <text class="menu-text logout-text">退出登录</text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useGlobalSafeArea } from '@/hooks/useSafeArea'
import { useSystemAdminStore } from '@/store/systemAdmin'
import SysTabBar from '@/pages-sys/components/sys-tab-bar/sys-tab-bar.vue'

defineOptions({
  name: 'Dashboard',
})

const { safeAreaInsetsTop } = useGlobalSafeArea()
const systemAdminStore = useSystemAdminStore()

// 左侧菜单弹窗相关
const showSideMenu = ref(false)

// 右滑手势相关
const touchStartX = ref(0)
const touchStartY = ref(0)

// 菜单操作函数
const openSideMenu = () => {
  console.log('打开左侧菜单')
  showSideMenu.value = true
}

const closeSideMenu = () => {
  console.log('关闭左侧菜单')
  showSideMenu.value = false
}

// 菜单导航函数 - 使用redirectTo直接跳转，避免页面栈堆积
const goToProfile = () => {
  console.log('进入个人信息')
  closeSideMenu()
  uni.redirectTo({
    url: '/pages-sys/profile/index',
  })
}

const goToChangePassword = () => {
  console.log('进入修改密码')
  closeSideMenu()
  uni.redirectTo({
    url: '/pages-sys/profile/change-password',
  })
}

const goToAbout = () => {
  console.log('进入关于系统')
  closeSideMenu()
  uni.showToast({
    title: '关于系统功能开发中',
    icon: 'none',
  })
}

const handleLogout = async () => {
  console.log('退出登录')
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        closeSideMenu()
        try {
          // 使用store的logout方法
          await systemAdminStore.logout()
          uni.showToast({
            title: '已退出登录',
            icon: 'success',
          })
          // 跳转到登录页面
          uni.reLaunch({
            url: '/pages-sys/login/login',
          })
        } catch (error) {
          console.error('退出登录失败:', error)
          uni.showToast({
            title: '退出失败',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 右滑手势支持
const handleTouchStart = (e: any) => {
  touchStartX.value = e.touches[0].clientX
  touchStartY.value = e.touches[0].clientY
}

const handleTouchEnd = (e: any) => {
  const touchEndX = e.changedTouches[0].clientX
  const touchEndY = e.changedTouches[0].clientY

  const deltaX = touchEndX - touchStartX.value
  const deltaY = touchEndY - touchStartY.value

  // 判断是否为右滑手势：向右滑动距离大于50px，且垂直滑动距离小于100px，且起始位置在屏幕左侧30px内
  if (deltaX > 50 && Math.abs(deltaY) < 100 && touchStartX.value < 30) {
    console.log('检测到右滑手势，打开菜单')
    openSideMenu()
  }
}

onMounted(() => {
  // 监听全局触摸事件以支持右滑打开菜单
  console.log('仪表盘页面挂载完成')
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

// 标题文字设为白色
:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

// 自定义左侧菜单按钮样式
:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;
}

// 菜单图标样式 - 使用外部样式类
.menu-icon {
  padding: 8px !important;
  font-size: 26px !important;
  color: #ffffff !important;
}

.page-content {
  flex: 1;
  padding: 20px;
  padding-bottom: 0; // TabBar会有自己的padding
  background-color: transparent;
  overflow: hidden;
  position: relative;
}

.content-area {
  flex: 1;
}

.content-placeholder {
  padding: 40px;
  background-color: white;
  border-radius: 8px;
  text-align: center;
}

.placeholder-text {
  color: #666;
  font-size: 16px;
}

// 左侧菜单弹窗样式
.side-menu-container {
  padding: 16px;
  height: 100vh;
  overflow-y: auto;
  background-color: white;
}

.user-info-section {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar-large {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
}

.user-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.user-role {
  font-size: 14px;
  color: #666;
  display: block;
}

.menu-section {
  margin-bottom: 20px;
}

.menu-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-title {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  display: block;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 4px;

  &:hover {
    background-color: #f5f7fa;
  }

  &:active {
    background-color: #e8f4fd;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.menu-text {
  font-size: 14px;
  color: #666;
  margin-left: 12px;
  flex: 1;
}

.logout-item {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 16px;

  .menu-text {
    color: #f44336;
    font-weight: 500;
  }
}

.logout-text {
  color: #f44336 !important;
  font-weight: 500;
}
</style>
