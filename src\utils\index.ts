import pagesConfig from '@/pages.json'
import { isMpWeixin } from './platform'
import { userTypeEnum, useGlobalRole } from '@/store/globalRole'
import type { PageConfig, RouteMeta, AuthCheckResult } from '@/types/route'

const { pages, subPackages, tabBar = { list: [] } } = { ...pagesConfig }

export const getLastPage = () => {
  // getCurrentPages() 至少有1个元素，所以不再额外判断
  // const lastPage = getCurrentPages().at(-1)
  // 上面那个在低版本安卓中打包会报错，所以改用下面这个【虽然我加了 src/interceptions/prototype.ts，但依然报错】
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

/**
 * 获取当前页面的完整路径
 * @returns 当前页面路径，如 '/pages-sys/system/index'
 */
export const getCurrentPagePath = (): string => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const currentRoute = (currentPage as any).route || (currentPage as any).__route__
  return currentRoute ? `/${currentRoute}` : ''
}

/**
 * 获取当前页面路由的 path 路径和 redirectPath 路径
 * path 如 '/pages/login/index'
 * redirectPath 如 '/pages/demo/base/route-interceptor'
 */
export const currRoute = () => {
  const lastPage = getLastPage()
  const currRoute = (lastPage as any).$page
  // console.log('lastPage.$page:', currRoute)
  // console.log('lastPage.$page.fullpath:', currRoute.fullPath)
  // console.log('lastPage.$page.options:', currRoute.options)
  // console.log('lastPage.options:', (lastPage as any).options)
  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱
  const { fullPath } = currRoute as { fullPath: string }
  // console.log(fullPath)
  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)
  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)
  return getUrlObj(fullPath)
}

const ensureDecodeURIComponent = (url: string) => {
  if (url.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/**
 * 解析 url 得到 path 和 query
 * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor
 * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}
 */
export const getUrlObj = (url: string) => {
  const [path, queryStr] = url.split('?')
  // console.log(path, queryStr)

  if (!queryStr) {
    return {
      path,
      query: {},
    }
  }
  const query: Record<string, string> = {}
  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    // console.log(key, value)
    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
  })
  return { path, query }
}

/**
 * 默认登录页面配置
 */
const DEFAULT_LOGIN_ROUTES = {
  [userTypeEnum.system]: '/pages-sys/login/login',
  [userTypeEnum.merchants]: '/pages-merchants/login/login',
  [userTypeEnum.user]: '/pages/user/login',
}

/**
 * 解析页面的路由 meta 配置
 * @param page 页面配置对象
 * @returns 解析后的 RouteMeta
 */
const parseRouteMeta = (page: PageConfig): RouteMeta => {
  // 从页面配置中提取 meta 信息
  const meta: RouteMeta = {}

  // 新版访问控制配置
  if (page.access) {
    meta.access = page.access
  }

  return meta
}

/**
 * 检查页面是否需要指定用户类型的认证
 * @param page 页面配置
 * @param userType 用户类型
 * @returns 是否需要认证
 */
export const checkPageRequireAuth = (page: PageConfig, userType: userTypeEnum): boolean => {
  const meta = parseRouteMeta(page)

  // 新版 access 配置
  if (meta.access) {
    const { requireAuth, allowedRoles } = meta.access

    // 白名单模式：如果明确设置为不需要认证，返回 false
    if (requireAuth === false) {
      return false
    }

    // 白名单模式：默认需要认证，如果没有限制角色，则所有登录用户都需要认证
    if (!allowedRoles || allowedRoles.length === 0) {
      return true
    }

    // 检查当前用户类型是否在允许的角色列表中
    return allowedRoles.includes(userType)
  }

  // 白名单模式：没有access配置的页面默认需要认证
  return true
}

/**
 * 获取页面的登录重定向路由
 * @param page 页面配置
 * @param userType 用户类型
 * @returns 登录页面路由
 */
export const getPageLoginRoute = (page: PageConfig, userType: userTypeEnum): string => {
  const meta = parseRouteMeta(page)

  // 检查是否有自定义重定向配置
  if (meta.access?.redirectTo) {
    return meta.access.redirectTo
  }

  // 使用默认登录页面
  return DEFAULT_LOGIN_ROUTES[userType] || DEFAULT_LOGIN_ROUTES[userTypeEnum.user]
}

/**
 * 得到所有的页面，包括主包和分包的
 * 白名单模式：可以传递 key 作为判断依据进行过滤
 * 如果没有传 key，则表示所有的 pages
 * 如果传递了 key, 则表示通过 access[key] 过滤页面
 * 例如：getAllPages('requireAuth') 获取明确需要认证的页面
 *      getAllPages() 获取所有页面
 */
export const getAllPages = (key?: string) => {
  // 这里处理主包
  const mainPages = [
    ...pages
      .filter((page) => !key || page.access?.[key])
      .map((page) => ({
        ...page,
        path: `/${page.path}`,
      })),
  ]
  // 这里处理分包
  const subPages: any[] = []
  subPackages.forEach((subPageObj) => {
    // console.log(subPageObj)
    const { root } = subPageObj

    subPageObj.pages
      .filter((page) => !key || page.access?.[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...mainPages, ...subPages]
  // console.log(`getAllPages by ${key} result: `, result)
  return result
}

/**
 * 根据用户类型获取需要认证的页面列表
 * @param userType 用户类型
 * @returns 需要认证的页面路径数组
 */
export const getAuthRequiredPages = (userType: userTypeEnum): string[] => {
  const allPages = getAllPages() // 获取所有页面，不过滤
  return allPages.filter((page) => checkPageRequireAuth(page, userType)).map((page) => page.path)
}

/**
 * 检查指定路径是否需要用户类型的认证
 * @param path 页面路径
 * @param userType 用户类型
 * @returns 权限检查结果
 */
export const checkPathAuth = (path: string, userType: userTypeEnum): AuthCheckResult => {
  const allPages = getAllPages() // 获取所有页面
  console.log('全部页面:', allPages)
  const targetPage = allPages.find((page) => page.path === path)

  if (!targetPage) {
    // 页面不存在，允许访问（由路由系统处理 404）
    return {
      allowed: true,
      reason: 'Page not found in config',
    }
  }

  const requireAuth = checkPageRequireAuth(targetPage, userType)

  if (!requireAuth) {
    return {
      allowed: true,
      reason: 'No auth required',
    }
  }

  const loginRoute = getPageLoginRoute(targetPage, userType)

  return {
    allowed: false,
    loginRoute,
    reason: `Requires ${userType} authentication`,
  }
}

/**
 * 根据微信小程序当前环境，判断应该获取的 baseUrl
 */
export const getEnvBaseUrl = () => {
  // 请求基准地址
  let baseUrl = import.meta.env.VITE_SERVER_BASEURL

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_DEVELOP || baseUrl
        break
      case 'trial':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_TRIAL || baseUrl
        break
      case 'release':
        baseUrl = import.meta.env.VITE_SERVER_BASEURL__WEIXIN_RELEASE || baseUrl
        break
    }
  }

  return baseUrl
}

/**
 * 根据微信小程序当前环境，判断应该获取的 UPLOAD_BASEURL
 */
export const getEnvBaseUploadUrl = () => {
  const globalRole = useGlobalRole()

  // 请求基准地址
  let baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL

  // 微信小程序端环境区分
  if (isMpWeixin) {
    const {
      miniProgram: { envVersion },
    } = uni.getAccountInfoSync()

    switch (envVersion) {
      case 'develop':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_DEVELOP || baseUploadUrl
        break
      case 'trial':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_TRIAL || baseUploadUrl
        break
      case 'release':
        baseUploadUrl = import.meta.env.VITE_UPLOAD_BASEURL__WEIXIN_RELEASE || baseUploadUrl
        break
    }
  }

  switch (globalRole.getRole) {
    case userTypeEnum.system:
      baseUploadUrl = baseUploadUrl + '/sys-upload'
      break
    case userTypeEnum.user:
      baseUploadUrl = ''
      break
    case userTypeEnum.merchants:
      baseUploadUrl = ''
      break
  }

  return baseUploadUrl
}
