<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '个人信息',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="profile-container">
    <!-- 导航栏 -->
    <Navbar
      title="个人信息"
      :fixed="true"
      :placeholder="true"
      custom-class="custom-navbar"
      @back="handleBack"
    />

    <!-- 内容区域 -->
    <view class="page-content">
      <view class="main-layout">
        <!-- 头像区域 -->
        <view class="avatar-section">
          <view class="avatar-wrapper" @click="handleAvatarUpload">
            <image
              v-if="userProfile?.avatar"
              :src="userProfile.avatar"
              class="avatar-image"
              mode="aspectFill"
            />
            <view v-else class="avatar-placeholder">
              <text class="iconfont-sys iconsys-user-filled"></text>
            </view>
            <!-- 上传状态指示器 -->
            <view v-if="uploadLoading" class="upload-overlay">
              <view class="upload-loading">
                <text class="loading-text">上传中...</text>
              </view>
            </view>
            <!-- 头像编辑图标 -->
            <view class="avatar-edit-icon">
              <text class="iconfont-sys iconsys-camera"></text>
            </view>
          </view>
          <view class="username-info">
            <text class="username">{{ userProfile?.username || '--' }}</text>
            <text class="status-text">{{ getStatusText(userProfile?.status || 1) }}</text>
          </view>
        </view>

        <!-- 个人信息表单 -->
        <view class="form-section">
          <view class="section-title">基本信息</view>

          <view class="form-list">
            <!-- 用户名 -->
            <view class="form-item readonly">
              <view class="form-label">
                <text class="label-text">用户名</text>
              </view>
              <view class="form-value">
                <text class="value-text">{{ userProfile?.username || '--' }}</text>
              </view>
            </view>

            <!-- 真实姓名 -->
            <view class="form-item" @click="showEditDialog('real_name')">
              <view class="form-label">
                <text class="label-text">真实姓名</text>
              </view>
              <view class="form-value">
                <text class="value-text">{{ userProfile?.real_name || '未设置' }}</text>
                <text class="iconfont-sys iconsys-arrow-right"></text>
              </view>
            </view>

            <!-- 手机号 -->
            <view class="form-item" @click="showEditDialog('phone')">
              <view class="form-label">
                <text class="label-text">手机号</text>
              </view>
              <view class="form-value">
                <text class="value-text">{{ userProfile?.phone || '未设置' }}</text>
                <text class="iconfont-sys iconsys-arrow-right"></text>
              </view>
            </view>

            <!-- 邮箱 -->
            <view class="form-item" @click="showEditDialog('email')">
              <view class="form-label">
                <text class="label-text">邮箱</text>
              </view>
              <view class="form-value">
                <text class="value-text">{{ userProfile?.email || '未设置' }}</text>
                <text class="iconfont-sys iconsys-arrow-right"></text>
              </view>
            </view>

            <!-- 性别 -->
            <view class="form-item" @click="showGenderPicker">
              <view class="form-label">
                <text class="label-text">性别</text>
              </view>
              <view class="form-value">
                <text class="value-text">{{ getGenderText(userProfile?.gender) }}</text>
                <text class="iconfont-sys iconsys-arrow-right"></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 账户信息 -->
        <view class="form-section">
          <view class="section-title">账户信息</view>

          <view class="form-list">
            <!-- 最后登录时间 -->
            <view class="form-item readonly">
              <view class="form-label">
                <text class="label-text">最后登录</text>
              </view>
              <view class="form-value">
                <text class="value-text">{{ formatLoginTime(userProfile?.last_login_date) }}</text>
              </view>
            </view>

            <!-- 登录IP -->
            <view class="form-item readonly">
              <view class="form-label">
                <text class="label-text">登录IP</text>
              </view>
              <view class="form-value">
                <text class="value-text">{{ userProfile?.last_login_ip || '--' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 微信绑定信息 -->
        <view class="form-section">
          <view class="section-title">微信绑定</view>

          <view class="form-list">
            <!-- 微信绑定状态 -->
            <view class="form-item" :class="{ readonly: !userProfile?.wechat_bound }">
              <view class="form-label">
                <text class="label-text">微信账号</text>
              </view>
              <view class="form-value">
                <view
                  v-if="userProfile?.wechat_bound"
                  class="wechat-info"
                  @click="handleWechatCardClick"
                >
                  <image
                    v-if="userProfile.wechat_avatar"
                    :src="userProfile.wechat_avatar"
                    class="wechat-avatar"
                    mode="aspectFill"
                  />
                  <view v-else class="wechat-avatar-placeholder">
                    <text class="iconfont-sys iconsys-user-filled"></text>
                  </view>
                  <view class="wechat-text-info">
                    <text class="wechat-nickname">
                      {{ userProfile.wechat_nickname || '微信用户' }}
                    </text>
                    <text class="wechat-status">已绑定</text>
                  </view>
                  <text class="action-text unbind" @click.stop="handleWechatUnbind">解绑</text>
                </view>
                <view v-else class="wechat-unbind" @click="handleWechatBind">
                  <text class="value-text">未绑定</text>
                  <view class="bind-btn" :class="{ loading: wechatBindLoading }">
                    <text v-if="!wechatBindLoading" class="bind-text">绑定微信</text>
                    <text v-else class="bind-text">绑定中...</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 编辑弹窗 -->
    <wd-popup
      v-model="showEditPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      custom-style="border-radius: 20px 20px 0 0;"
    >
      <view class="edit-popup">
        <view class="popup-header">
          <view class="popup-title">编辑{{ getFieldLabel(editField) }}</view>
          <view class="popup-actions">
            <text class="action-btn cancel" @click="closeEditDialog">取消</text>
            <text class="action-btn confirm" @click="confirmEdit">确定</text>
          </view>
        </view>
        <view class="popup-content">
          <wd-input
            v-model="editValue"
            :placeholder="`请输入${getFieldLabel(editField)}`"
            :maxlength="getFieldMaxLength(editField)"
            custom-class="edit-input"
          />
        </view>
      </view>
    </wd-popup>

    <!-- 性别选择器 -->
    <wd-popup
      v-model="showGenderPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      custom-style="border-radius: 20px 20px 0 0;"
    >
      <view class="gender-popup">
        <view class="popup-header">
          <view class="popup-title">选择性别</view>
          <view class="popup-actions">
            <text class="action-btn cancel" @click="closeGenderPicker">取消</text>
            <text class="action-btn confirm" @click="confirmGender">确定</text>
          </view>
        </view>
        <view class="popup-content">
          <view class="gender-options">
            <view
              v-for="option in genderOptions"
              :key="option.value"
              class="gender-option"
              :class="{ active: selectedGender === option.value }"
              @click="selectGender(option.value)"
            >
              <text class="option-text">{{ option.label }}</text>
              <text
                v-if="selectedGender === option.value"
                class="iconfont-sys iconsys-check option-check"
              ></text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 微信信息详情弹窗 -->
    <wd-popup
      v-model="showWechatInfoPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      custom-style="border-radius: 20px 20px 0 0;"
    >
      <view class="wechat-info-popup">
        <view class="popup-header">
          <view class="popup-title">微信信息</view>
          <view class="popup-actions">
            <text class="action-btn cancel" @click="closeWechatInfoPopup">关闭</text>
          </view>
        </view>
        <view class="popup-content">
          <view class="wechat-detail-info">
            <view class="wechat-avatar-large">
              <image
                v-if="userProfile?.wechat_avatar"
                :src="userProfile.wechat_avatar"
                class="avatar-large"
                mode="aspectFill"
              />
              <view v-else class="avatar-large-placeholder">
                <text class="iconfont-sys iconsys-user-filled"></text>
              </view>
            </view>
            <view class="wechat-detail-text">
              <text class="detail-nickname">{{ userProfile?.wechat_nickname || '微信用户' }}</text>
              <text class="detail-status">已绑定微信账号</text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 解绑微信密码验证弹窗 -->
    <wd-popup
      v-model="showUnbindPasswordPopup"
      position="bottom"
      :safe-area-inset-bottom="true"
      custom-style="border-radius: 20px 20px 0 0;"
    >
      <view class="unbind-popup">
        <view class="popup-header">
          <view class="popup-title">解绑微信</view>
          <view class="popup-actions">
            <text class="action-btn cancel" @click="closeUnbindPasswordPopup">取消</text>
            <text class="action-btn confirm" @click="confirmUnbindWechat">确定</text>
          </view>
        </view>
        <view class="popup-content">
          <view class="unbind-tip">
            <text class="tip-text">为了账户安全，解绑微信需要验证密码</text>
          </view>
          <wd-input
            v-model="unbindPassword"
            show-password
            placeholder="请输入登录密码"
            custom-class="unbind-password-input"
          />
        </view>
      </view>
    </wd-popup>

    <!-- 加载状态 -->
    <wd-loading v-if="loading" custom-class="page-loading" />
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useSystemAdminStore } from '@/store/systemAdmin'
import {
  getProfileApi,
  updateProfileApi,
  bindWechatApi,
  unbindWechatApi,
  type IUserProfile,
  type IUpdateProfileForm,
  type IWechatBindForm,
  type IWechatUnbindForm,
} from '@/api/sys/systemAuthApi'
import useUpload from '@/hooks/useUpload'
import WxUtil from '@/utils/wxUtil'

// Store
const systemAdminStore = useSystemAdminStore()

// 头像上传
const {
  loading: uploadLoading,
  error: uploadError,
  data: uploadData,
  run: startUpload,
} = useUpload<string>()

// 状态管理
const loading = ref(false)
const userProfile = ref<IUserProfile | null>(null)

// 编辑弹窗状态
const showEditPopup = ref(false)
const editField = ref<keyof IUpdateProfileForm>('real_name')
const editValue = ref('')

// 性别选择器状态
const showGenderPopup = ref(false)
const selectedGender = ref<number | null>(null)

// 微信相关状态
const wechatBindLoading = ref(false)
const wechatUnbindLoading = ref(false)
const showUnbindPasswordPopup = ref(false)
const showWechatInfoPopup = ref(false)
const unbindPassword = ref('')

// 性别选项
const genderOptions = [
  { label: '男', value: 1 },
  { label: '女', value: 2 },
  { label: '未知', value: 3 },
]

// 计算属性
const currentGender = computed(() => {
  return userProfile.value?.gender || null
})

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1:
      return '正常'
    case 2:
      return '禁用'
    case 3:
      return '锁定'
    default:
      return '未知'
  }
}

// 获取性别文本
const getGenderText = (gender?: number | null) => {
  switch (gender) {
    case 1:
      return '男'
    case 2:
      return '女'
    case 3:
      return '未知'
    default:
      return '未设置'
  }
}

// 格式化登录时间
const formatLoginTime = (dateStr?: string | null) => {
  if (!dateStr) return '--'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (error) {
    return '--'
  }
}

// 获取字段标签
const getFieldLabel = (field: keyof IUpdateProfileForm) => {
  const labels = {
    real_name: '真实姓名',
    phone: '手机号',
    email: '邮箱',
    avatar: '头像',
    gender: '性别',
  }
  return labels[field] || ''
}

// 获取字段最大长度
const getFieldMaxLength = (field: keyof IUpdateProfileForm) => {
  const maxLengths = {
    real_name: 20,
    phone: 11,
    email: 50,
    avatar: 200,
    gender: 1,
  }
  return maxLengths[field] || 50
}

// 获取输入框类型
const getFieldInputType = (field: keyof IUpdateProfileForm) => {
  const types: Record<keyof IUpdateProfileForm, string> = {
    real_name: 'text',
    phone: 'tel',
    email: 'email',
    avatar: 'text',
    gender: 'text',
  }
  return types[field] || 'text'
}

// 加载个人信息
const loadProfile = async () => {
  loading.value = true
  try {
    const result = await getProfileApi()
    if (result.code === 200) {
      userProfile.value = result.data
      // 同步更新 store 中的信息
      systemAdminStore.updateAdminInfo(result.data)
    } else {
      uni.showToast({
        title: result.message || '获取个人信息失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取个人信息失败:', error)
    uni.showToast({
      title: '获取个人信息失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 显示编辑弹窗
const showEditDialog = (field: keyof IUpdateProfileForm) => {
  editField.value = field
  editValue.value = (userProfile.value?.[field] as string) || ''
  showEditPopup.value = true
}

// 关闭编辑弹窗
const closeEditDialog = () => {
  showEditPopup.value = false
  editValue.value = ''
}

// 确认编辑
const confirmEdit = async () => {
  if (!editValue.value.trim()) {
    uni.showToast({
      title: `请输入${getFieldLabel(editField.value)}`,
      icon: 'none',
    })
    return
  }

  // 验证输入
  if (editField.value === 'phone' && !/^1[3-9]\d{9}$/.test(editValue.value)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none',
    })
    return
  }

  if (editField.value === 'email' && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editValue.value)) {
    uni.showToast({
      title: '请输入正确的邮箱地址',
      icon: 'none',
    })
    return
  }

  try {
    const updateData: IUpdateProfileForm = {
      [editField.value]: editValue.value.trim(),
    }

    const result = await updateProfileApi(updateData)
    if (result.code === 200) {
      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })

      // 更新本地数据
      if (userProfile.value) {
        ;(userProfile.value as any)[editField.value] = editValue.value.trim()
      }

      closeEditDialog()
    } else {
      uni.showToast({
        title: result.message || '更新失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('更新个人信息失败:', error)
    uni.showToast({
      title: '更新失败',
      icon: 'none',
    })
  }
}

// 显示性别选择器
const showGenderPicker = () => {
  selectedGender.value = currentGender.value
  showGenderPopup.value = true
}

// 关闭性别选择器
const closeGenderPicker = () => {
  showGenderPopup.value = false
  selectedGender.value = null
}

// 选择性别
const selectGender = (gender: number) => {
  selectedGender.value = gender
}

// 确认性别选择
const confirmGender = async () => {
  if (selectedGender.value === null || selectedGender.value === currentGender.value) {
    closeGenderPicker()
    return
  }

  try {
    const updateData: IUpdateProfileForm = {
      gender: selectedGender.value,
    }

    const result = await updateProfileApi(updateData)
    if (result.code === 200) {
      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })

      // 更新本地数据
      if (userProfile.value) {
        userProfile.value.gender = selectedGender.value
      }

      closeGenderPicker()
    } else {
      uni.showToast({
        title: result.message || '更新失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('更新性别失败:', error)
    uni.showToast({
      title: '更新失败',
      icon: 'none',
    })
  }
}

// 返回功能 - 返回到仪表板
const handleBack = () => {
  uni.redirectTo({
    url: '/pages-sys/dashboard/index',
  })
}

// 头像上传功能
const handleAvatarUpload = () => {
  uni.showModal({
    title: '更换头像',
    content: '是否要更换头像？',
    success: (res) => {
      if (res.confirm) {
        startUpload('/sys-user/avatar', 'avatar')
      }
    },
  })
}

// 微信卡片点击处理（显示微信信息详情）
const handleWechatCardClick = () => {
  showWechatInfoPopup.value = true
}

// 微信解绑处理
const handleWechatUnbind = () => {
  showUnbindPasswordPopup.value = true
}

// 关闭微信信息弹窗
const closeWechatInfoPopup = () => {
  showWechatInfoPopup.value = false
}

// 微信绑定处理
const handleWechatBind = async () => {
  wechatBindLoading.value = true
  try {
    console.log('开始微信绑定')

    // 1. 获取用户头像和昵称（使用微信官方组件）
    // 跳转到微信信息填写页面，用户填写完成后会回调处理绑定
    const userInfo = await getUserProfileFromWechat()

    // 2. 调用绑定API（code已经在微信信息页面获取并传回）
    const bindForm: IWechatBindForm = {
      code: userInfo.code, // 从微信信息页面传回的最新code
      nickname: userInfo.nickname,
      avatar: userInfo.avatar,
    }

    const result = await bindWechatApi(bindForm)
    if (result.code === 200) {
      uni.showToast({
        title: '微信绑定成功',
        icon: 'success',
      })

      // 重新加载个人信息
      await loadProfile()
    } else {
      uni.showToast({
        title: result.message || '微信绑定失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('微信绑定失败:', error)
    uni.showToast({
      title: '微信绑定失败',
      icon: 'none',
    })
  } finally {
    wechatBindLoading.value = false
  }
}

// 获取微信用户信息（头像和昵称）
const getUserProfileFromWechat = (): Promise<{
  nickname: string
  avatar: string
  code: string
}> => {
  return new Promise((resolve, reject) => {
    // 跳转到微信信息填写页面
    uni.redirectTo({
      url: '/pages-sys/profile/wechat-info?action=bind',
      fail: (err) => {
        console.error('跳转微信信息页面失败:', err)
        reject(new Error('跳转失败'))
      },
    })
    // 注意：使用redirectTo后无法使用events通信，微信信息页面完成后直接返回仪表板
    resolve({ nickname: '', avatar: '', code: '' }) // 占位返回
  })
}

// 关闭解绑密码弹窗
const closeUnbindPasswordPopup = () => {
  showUnbindPasswordPopup.value = false
  // 延迟清空密码，避免弹窗关闭动画时看到清空过程
  setTimeout(() => {
    unbindPassword.value = ''
  }, 200)
}

// 确认解绑微信
const confirmUnbindWechat = async () => {
  if (!unbindPassword.value.trim()) {
    uni.showToast({
      title: '请输入登录密码',
      icon: 'none',
    })
    return
  }

  wechatUnbindLoading.value = true
  try {
    const unbindForm: IWechatUnbindForm = {
      password: unbindPassword.value.trim(),
    }

    const result = await unbindWechatApi(unbindForm)
    if (result.code === 200) {
      uni.showToast({
        title: '微信解绑成功',
        icon: 'success',
      })

      // 关闭弹窗并重新加载个人信息
      closeUnbindPasswordPopup()
      await loadProfile()
    } else {
      uni.showToast({
        title: result.message || '微信解绑失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('微信解绑失败:', error)
    uni.showToast({
      title: '微信解绑失败',
      icon: 'none',
    })
  } finally {
    wechatUnbindLoading.value = false
  }
}

// 监听上传结果
watch(
  () => uploadData.value,
  async (newData) => {
    if (newData) {
      try {
        // 解析上传返回的数据
        let avatarUrl = ''
        try {
          const uploadResult = JSON.parse(newData)

          // 检查返回结果的格式
          if (uploadResult.code === 200 && uploadResult.data) {
            // 使用 file_url 作为头像地址
            avatarUrl = uploadResult.data.file_url
          } else {
            throw new Error(uploadResult.message || '上传失败')
          }
        } catch (e) {
          console.error('解析上传结果失败:', e)
          uni.showToast({
            title: '上传结果解析失败',
            icon: 'none',
          })
          return
        }

        // 检查是否获取到有效的头像 URL
        if (!avatarUrl) {
          uni.showToast({
            title: '获取头像地址失败',
            icon: 'none',
          })
          return
        }

        // 更新头像
        const updateData: IUpdateProfileForm = {
          avatar: avatarUrl,
        }

        const result = await updateProfileApi(updateData)
        if (result.code === 200) {
          uni.showToast({
            title: '头像更新成功',
            icon: 'success',
          })

          // 更新本地数据
          if (userProfile.value) {
            userProfile.value.avatar = avatarUrl
          }

          // 同步更新 store 中的信息
          systemAdminStore.updateAdminInfo({ avatar: avatarUrl })
        } else {
          uni.showToast({
            title: result.message || '头像更新失败',
            icon: 'none',
          })
        }
      } catch (error) {
        console.error('更新头像失败:', error)
        uni.showToast({
          title: '头像更新失败',
          icon: 'none',
        })
      }
    }
  },
)

// 监听上传错误
watch(
  () => uploadError.value,
  (hasError) => {
    if (hasError) {
      uni.showToast({
        title: '头像上传失败',
        icon: 'none',
      })
    }
  },
)

// 页面挂载
onMounted(() => {
  console.log('个人信息页面挂载')
  loadProfile()
})

// 页面显示时重新加载数据
onShow(() => {
  console.log('个人信息页面显示，重新加载数据')
  loadProfile()
})
</script>

<style lang="scss" scoped>
.profile-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;

  .wd-navbar__title {
    color: white !important;
    font-size: 20px !important;
    font-weight: bold !important;
  }

  .wd-navbar__left .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;

    .wd-icon {
      font-size: 26px !important;
      color: white !important;
    }
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.main-layout {
  padding: 20px;
}

// 头像区域
.avatar-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.avatar-wrapper {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  &:hover .avatar-edit-icon {
    opacity: 1;
  }
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e0e0;

  .iconfont-sys {
    font-size: 40px;
    color: #999;
  }
}

.username-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.username {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.status-text {
  font-size: 14px;
  color: #4caf50;
}

// 表单区域
.form-section {
  background: white;
  border-radius: 12px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  padding: 16px 20px 12px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.form-list {
  padding: 0;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:not(.readonly) {
    &:active {
      background-color: #f8f8f8;
    }
  }

  &.readonly {
    opacity: 0.8;
  }
}

.form-label {
  width: 80px;
  flex-shrink: 0;
}

.label-text {
  font-size: 14px;
  color: #666;
}

.form-value {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.value-text {
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.iconfont-sys {
  font-size: 14px;
  color: #ccc;
}

// 弹窗样式
.edit-popup,
.gender-popup {
  background: white;
  min-height: 300px;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.popup-actions {
  display: flex;
  gap: 20px;
}

.action-btn {
  font-size: 14px;

  &.cancel {
    color: #999;
  }

  &.confirm {
    color: #4285f4;
  }

  &:active {
    opacity: 0.7;
  }
}

.popup-content {
  padding: 20px;
}

:deep(.edit-input) {
  .wd-input {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
  }
}

// 性别选择器
.gender-options {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.gender-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &.active {
    .option-text {
      color: #4285f4;
      font-weight: 500;
    }

    .option-check {
      color: #4285f4;
    }
  }

  &:active {
    background-color: #f8f8f8;
  }
}

.option-text {
  font-size: 16px;
  color: #333;
}

.option-check {
  font-size: 16px;
}

// 加载状态
:deep(.page-loading) {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

// 上传状态指示器
.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-loading {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

// 头像编辑图标
.avatar-edit-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;

  .iconfont-sys {
    font-size: 20px;
    color: white;
  }
}

// 微信相关样式
.wechat-info {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.wechat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
}

.wechat-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  .iconfont-sys {
    font-size: 20px;
    color: #999;
  }
}

.wechat-text-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.wechat-nickname {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.wechat-status {
  font-size: 12px;
  color: #4caf50;
}

.wechat-unbind {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.bind-btn {
  background: linear-gradient(135deg, #07c160, #06ad56);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  transition: all 0.2s ease;

  &.loading {
    opacity: 0.7;
  }

  &:active {
    transform: scale(0.95);
  }
}

.bind-text {
  color: white;
  font-size: 12px;
}

.action-text {
  font-size: 12px;
  color: #666;

  &.unbind {
    color: #ff4757;
  }

  &:active {
    opacity: 0.7;
  }
}

// 微信信息详情弹窗样式
.wechat-info-popup {
  background: white;
  min-height: 320px;
}

.wechat-detail-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
}

.wechat-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-large {
  width: 100%;
  height: 100%;
}

.avatar-large-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;

  .iconfont-sys {
    font-size: 32px;
    color: #999;
  }
}

.wechat-detail-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.detail-nickname {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.detail-status {
  font-size: 14px;
  color: #4caf50;
}

// 解绑弹窗样式
.unbind-popup {
  background: white;
  min-height: 280px;
}

.unbind-tip {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff3cd;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
}

.tip-text {
  font-size: 14px;
  color: #856404;
  line-height: 1.5;
}

:deep(.unbind-password-input) {
  .wd-input {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
  }
}
</style>
