<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户表单',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="merchant-form-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="getPageTitle()"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <view class="main-layout">
        <view class="steps-wrapper">
          <wd-steps :active="currentStep" align-center>
            <wd-step title="基本信息" />
            <wd-step title="联系信息" />
            <wd-step title="营业信息" />
            <wd-step title="其他信息" />
          </wd-steps>
        </view>

        <view class="merchant-form">
          <wd-form ref="formRef" :model="merchantForm" :rules="formRules" errorType="message">
            <!-- 步骤1：基本信息 -->
            <view v-show="currentStep === 0" class="form-section">
              <view class="section-title">基本信息</view>

              <!-- 商户头像上传 -->
              <view class="avatar-section">
                <text class="avatar-label">商户头像</text>
                <view class="avatar-upload">
                  <view class="avatar-container" @click="chooseAvatar">
                    <wd-img
                      v-if="merchantForm.avatar"
                      :src="merchantForm.avatar"
                      width="80px"
                      height="80px"
                      mode="aspectFill"
                      custom-class="avatar-image"
                      @error="handleAvatarError"
                      @load="handleAvatarLoad"
                    />
                    <view v-else class="avatar-placeholder">
                      <wd-icon name="camera" size="24" color="#999" />
                      <text class="placeholder-text">点击上传头像</text>
                    </view>
                    <!-- 上传状态指示器 -->
                    <view v-if="avatarUploadLoading" class="upload-overlay">
                      <view class="upload-loading">
                        <wd-loading />
                        <text class="loading-text">上传中...</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>

              <wd-cell-group border>
                <!-- 商户名称 -->
                <wd-input
                  label="商户名称"
                  label-width="100px"
                  prop="merchant_name"
                  v-model="merchantForm.merchant_name"
                  placeholder="请输入商户名称"
                  :maxlength="50"
                  show-word-limit
                  clearable
                  :rules="formRules.merchant_name"
                  @blur="handleMerchantNameBlur"
                />

                <!-- 商户编码 -->
                <wd-input
                  label="商户编码"
                  label-width="100px"
                  prop="merchant_code"
                  v-model="merchantForm.merchant_code"
                  placeholder="请输入商户编码"
                  :disabled="isEditMode"
                  :maxlength="20"
                  show-word-limit
                  clearable
                  :rules="formRules.merchant_code"
                  @blur="handleMerchantCodeBlur"
                />

                <!-- 商户分类 -->
                <wd-picker
                  label="商户分类"
                  label-width="100px"
                  prop="category_id"
                  v-model="merchantForm.category_id"
                  :columns="categoryOptions"
                  placeholder="请选择商户分类"
                  @confirm="onCategoryChange"
                />

                <!-- 营业执照号 -->
                <wd-input
                  label="营业执照号"
                  label-width="100px"
                  prop="business_license"
                  v-model="merchantForm.business_license"
                  placeholder="请输入营业执照号"
                  :maxlength="30"
                  show-word-limit
                  clearable
                  :rules="formRules.business_license"
                  @blur="handleBusinessLicenseBlur"
                />
              </wd-cell-group>
            </view>

            <!-- 步骤2：联系信息 -->
            <view v-show="currentStep === 1" class="form-section">
              <view class="section-title">联系信息</view>

              <wd-cell-group border>
                <!-- 联系电话 -->
                <wd-input
                  label="联系电话"
                  label-width="100px"
                  prop="phone"
                  v-model="merchantForm.phone"
                  placeholder="请输入联系电话"
                  :maxlength="11"
                  clearable
                  :rules="formRules.phone"
                  @blur="handlePhoneBlur"
                />

                <!-- 邮箱地址 -->
                <wd-input
                  label="邮箱地址"
                  label-width="100px"
                  prop="email"
                  v-model="merchantForm.email"
                  placeholder="请输入邮箱地址"
                  clearable
                  :rules="formRules.email"
                  @blur="handleEmailBlur"
                />

                <!-- 经营地址 -->
                <wd-textarea
                  label="经营地址"
                  label-width="100px"
                  prop="address"
                  v-model="merchantForm.address"
                  placeholder="请输入详细的经营地址"
                  :maxlength="200"
                  show-word-limit
                  auto-height
                  clearable
                  @input="onAddressInput"
                />
              </wd-cell-group>

              <!-- 地理位置 -->
              <view class="location-section">
                <view class="section-subtitle">地理位置</view>

                <!-- 地图显示区域 -->
                <map
                  v-if="merchantForm.longitude && merchantForm.latitude"
                  :longitude="merchantForm.longitude"
                  :latitude="merchantForm.latitude"
                  :markers="mapMarkers"
                  :show-location="true"
                  enable-zoom
                  enable-scroll
                  enable-rotate
                  show-compass
                  class="merchant-location-map"
                  @markertap="onMarkerTap"
                />

                <wd-cell-group border>
                  <!-- 获取当前位置按钮 -->
                  <view class="location-actions">
                    <wd-button size="small" type="info" @click="getCurrentLocation">
                      <wd-icon name="location" />
                      获取当前位置
                    </wd-button>
                    <wd-button
                      size="small"
                      type="primary"
                      @click="openMapPicker"
                      v-if="mapSupported"
                    >
                      <wd-icon name="map" />
                      地图选点
                    </wd-button>
                  </view>

                  <!-- 位置信息显示 -->
                  <view
                    v-if="merchantForm.longitude && merchantForm.latitude"
                    class="location-info"
                  >
                    <text class="address-text" v-if="merchantForm.address">
                      地址: {{ merchantForm.address }}
                    </text>
                    <text v-else class="location-text">位置已设置</text>
                  </view>
                </wd-cell-group>

                <!-- 地图选点弹出层 -->
                <wd-popup
                  v-model="showMapPicker"
                  position="bottom"
                  :safe-area-inset-bottom="true"
                  custom-style="border-radius: 20rpx 20rpx 0 0; height: 55vh;"
                >
                  <view class="map-picker-popup">
                    <view class="popup-header">
                      <text class="popup-title">选择商户位置</text>
                    </view>

                    <view class="map-picker-container">
                      <map
                        id="pickerMap"
                        :longitude="pickerMapCenter.longitude"
                        :latitude="pickerMapCenter.latitude"
                        :markers="pickerMarkers"
                        :show-location="true"
                        enable-zoom
                        enable-scroll
                        enable-rotate
                        class="picker-map"
                        @tap="onMapTap"
                        @poitap="onPoiTap"
                        @markertap="onPickerMarkerTap"
                      />
                    </view>

                    <!-- 选中位置信息显示 -->
                    <view class="selected-location-info" v-if="selectedLocation.latitude">
                      <text class="selected-address">
                        {{ selectedLocationAddress || '正在获取地址信息...' }}
                      </text>
                    </view>

                    <view class="popup-actions">
                      <wd-button @click="cancelMapPicker" plain size="large">取消</wd-button>
                      <wd-button
                        @click="confirmMapPicker"
                        type="primary"
                        size="large"
                        :disabled="!selectedLocation.latitude"
                      >
                        确定选择
                      </wd-button>
                    </view>
                  </view>
                </wd-popup>
              </view>
            </view>

            <!-- 步骤3：营业信息 -->
            <view v-show="currentStep === 2" class="form-section">
              <view class="section-title">营业信息</view>

              <wd-cell-group border>
                <!-- 营业执照照片 -->
                <view class="license-section">
                  <text class="license-label">营业执照照片</text>
                  <view class="license-upload">
                    <view class="license-container" @click="chooseLicensePhoto">
                      <wd-img
                        v-if="merchantForm.license_photo"
                        :src="merchantForm.license_photo"
                        width="160px"
                        height="120px"
                        mode="aspectFit"
                        custom-class="license-image"
                        @error="handleLicenseError"
                        @load="handleLicenseLoad"
                      />
                      <view v-else class="license-placeholder">
                        <wd-icon name="camera" size="24" color="#999" />
                        <text class="placeholder-text">点击上传营业执照</text>
                      </view>
                      <!-- 上传状态指示器 -->
                      <view v-if="licenseUploadLoading" class="upload-overlay">
                        <view class="upload-loading">
                          <wd-loading />
                          <text class="loading-text">上传中...</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>

                <!-- 平台佣金比例 -->
                <wd-input
                  label="平台佣金比例"
                  label-width="100px"
                  prop="platform_commission_rate"
                  v-model="merchantForm.platform_commission_rate"
                  placeholder="请输入佣金比例（如：0.05）"
                  type="digit"
                  clearable
                  :rules="formRules.platform_commission_rate"
                />

                <!-- 商户状态 -->
                <wd-picker
                  label="商户状态"
                  label-width="100px"
                  prop="status"
                  v-model="merchantForm.status"
                  :columns="statusOptions"
                  placeholder="请选择商户状态"
                  :rules="formRules.status"
                  @confirm="onStatusChange"
                />

                <!-- 账单清零日期 -->
                <wd-cell title="账单清零日期" @click="openClearDatePicker" clickable>
                  <view class="clear-date-display">
                    <text class="date-text">{{ clearDateDisplayText }}</text>
                    <wd-icon name="arrow-right" size="16" color="#999" />
                  </view>
                </wd-cell>

                <!-- 排序权重 -->
                <wd-cell title="排序权重">
                  <view class="sort-order-container">
                    <wd-input-number
                      v-model="merchantForm.sort_order"
                      placeholder="请输入排序权重"
                      :min="0"
                      :max="9999"
                      :step="1"
                      :precision="0"
                      style="width: 120px"
                    />
                  </view>
                </wd-cell>
              </wd-cell-group>

              <!-- 日期选择弹出层 -->
              <wd-popup
                v-model="showClearDatePicker"
                position="bottom"
                :safe-area-inset-bottom="true"
                custom-style="border-radius: 20rpx 20rpx 0 0;"
              >
                <view class="date-picker-popup">
                  <view class="popup-header">
                    <text class="popup-title">选择账单清零日期</text>
                    <text class="popup-subtitle">每年的这个日期将自动清零账单数据</text>
                  </view>

                  <view class="picker-container">
                    <wd-datetime-picker-view
                      v-model="tempClearDate"
                      type="date"
                      :filter="clearDateFilter"
                      :formatter="clearDateFormatter"
                      :min-date="clearDateMinDate"
                      :max-date="clearDateMaxDate"
                      style="height: 240px"
                      @change="onTempDateChange"
                    />
                  </view>

                  <view class="popup-actions">
                    <wd-button @click="cancelDatePicker" plain size="large">取消</wd-button>
                    <wd-button @click="confirmDatePicker" type="primary" size="large">
                      确定
                    </wd-button>
                  </view>
                </view>
              </wd-popup>
            </view>

            <!-- 步骤4：其他信息 -->
            <view v-show="currentStep === 3" class="form-section">
              <view class="section-title">其他信息</view>

              <wd-cell-group border>
                <!-- 商户描述 -->
                <wd-textarea
                  label="商户描述"
                  label-width="100px"
                  prop="description"
                  v-model="merchantForm.description"
                  placeholder="请输入商户描述信息"
                  :maxlength="500"
                  show-word-limit
                  auto-height
                  clearable
                  @input="onDescriptionInput"
                />

                <!-- 备注 -->
                <wd-textarea
                  label="备注"
                  label-width="100px"
                  prop="remark"
                  v-model="merchantForm.remark"
                  placeholder="请输入备注信息"
                  :maxlength="200"
                  show-word-limit
                  auto-height
                  clearable
                  @input="onRemarkInput"
                />
              </wd-cell-group>
            </view>
          </wd-form>

          <!-- 步骤导航按钮 -->
          <view class="step-actions">
            <!-- 上一步 -->
            <wd-button
              v-if="currentStep > 0"
              @click="prevStep"
              size="large"
              custom-class="prev-btn"
            >
              上一步
            </wd-button>
            <view v-else></view>

            <!-- 编辑模式下的保存按钮 -->
            <wd-button
              v-if="isEditMode"
              type="success"
              @click="saveCurrentStep"
              size="large"
              :loading="submitting"
              custom-class="save-btn"
            >
              {{ getStepSaveText() }}
            </wd-button>

            <!-- 下一步/完成创建 -->
            <wd-button
              v-if="currentStep < 3"
              type="primary"
              @click="nextStep"
              size="large"
              custom-class="next-btn"
            >
              下一步
            </wd-button>
            <wd-button
              v-else-if="isAddMode"
              type="primary"
              @click="handleSubmit"
              size="large"
              :loading="submitting"
              custom-class="submit-btn"
            >
              创建商户
            </wd-button>
            <view v-else></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 头像裁剪 -->
    <wd-img-cropper
      v-model="showAvatarCropper"
      :img-src="tempAvatarSrc"
      aspect-ratio="1:1"
      @confirm="handleAvatarCropConfirm"
      @cancel="handleAvatarCropCancel"
    />

    <!-- 营业执照裁剪 -->
    <wd-img-cropper
      v-model="showLicenseCropper"
      :img-src="tempLicenseSrc"
      aspect-ratio="3:2"
      @confirm="handleLicenseCropConfirm"
      @cancel="handleLicenseCropCancel"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {
  getSystemMerchantDetailApi,
  createSystemMerchantApi,
  updateSystemMerchantApi,
} from '@/api/sys/systemMerchantApi'
import { getSystemMerchantCategorySelectItemsApi } from '@/api/sys/systemMerchantCategoryApi'
import type {
  ISysMerchantCreateRequest,
  ISysMerchantUpdateRequest,
  ISysMerchantDetailResponse,
} from '@/api/sys/types/merchant'
import useUpload from '@/hooks/useUpload'

defineOptions({
  name: 'MerchantForm',
})

// 页面参数
const pageParams = ref<{
  mode: 'add' | 'edit'
  merchantId?: string
}>({
  mode: 'add',
})

// 当前步骤
const currentStep = ref(0)

// 计算属性
const isAddMode = computed(() => pageParams.value.mode === 'add')
const isEditMode = computed(() => pageParams.value.mode === 'edit')

// 获取页面标题
const getPageTitle = () => {
  switch (pageParams.value.mode) {
    case 'add':
      return '新增'
    case 'edit':
      return '编辑'
    default:
      return '商户表单'
  }
}

// 获取步骤保存文本
const getStepSaveText = () => {
  return '保存'
}

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 商户详情（查看模式）
const merchantDetail = ref<any>(null)

// 商户表单数据
const merchantForm = reactive({
  merchant_name: '',
  merchant_code: '',
  category_id: '',
  phone: '',
  email: '',
  address: '',
  business_license: '',
  license_photo: '',
  avatar: '',
  description: '',
  platform_commission_rate: '',
  status: 1, // 默认正常营业
  auto_clear_date: new Date().getTime(), // 时间戳类型
  sort_order: 0, // 数字类型
  remark: '',
  longitude: 0, // 经度
  latitude: 0, // 纬度
})

// 商户分类选项 - 从API获取
const categoryOptions = ref([])

// 状态选项
const statusOptions = ref([
  { label: '正常营业', value: 1 },
  { label: '临时关闭', value: 2 },
  { label: '永久关闭', value: 3 },
])

// 日期选择弹出层相关
const showClearDatePicker = ref(false)
const tempClearDate = ref(new Date().getTime())

// 账单清零日期范围（以当前年为准）
const currentYear = new Date().getFullYear()
const clearDateMinDate = ref(new Date(`${currentYear}-01-01`).getTime())
const clearDateMaxDate = ref(new Date(`${currentYear}-12-31`).getTime())

// 日期过滤器 - 只显示月份和日期，年份以当前年为准
const clearDateFilter = (type: string, values: number[]) => {
  switch (type) {
    case 'year':
      // 只显示当前年，用作参照年份
      return [currentYear]
    case 'month':
      // 显示所有月份 1-12
      return values
    case 'date':
      // 根据选中的月份动态显示对应的日期数
      const selectedDate = new Date(tempClearDate.value)
      const selectedMonth = selectedDate.getMonth() + 1
      const daysInMonth = new Date(currentYear, selectedMonth, 0).getDate()
      return values.filter((day) => day <= daysInMonth)
    default:
      return values
  }
}

// 日期格式化函数 - 只显示月日
const clearDateFormatter = (type: string, value: string | number): string => {
  const numValue = typeof value === 'string' ? parseInt(value) : value
  switch (type) {
    case 'year':
      return '每年' // 年份显示为"每年"
    case 'month':
      return numValue + '月'
    case 'date':
      return numValue + '日'
    default:
      return String(value)
  }
}

// 计算显示文本
const clearDateDisplayText = computed(() => {
  if (!merchantForm.auto_clear_date) {
    return '请选择清零日期'
  }
  const date = new Date(merchantForm.auto_clear_date)
  return `每年${date.getMonth() + 1}月${date.getDate()}日`
})

// 打开日期选择器
const openClearDatePicker = () => {
  console.log('openClearDatePicker 被调用')

  tempClearDate.value = merchantForm.auto_clear_date || new Date().getTime()
  showClearDatePicker.value = true

  console.log('打开日期选择器，tempClearDate:', tempClearDate.value)
  console.log('showClearDatePicker:', showClearDatePicker.value)
}

// 临时日期变化
const onTempDateChange = (event: any) => {
  tempClearDate.value = event.value
}

// 确认选择
const confirmDatePicker = () => {
  merchantForm.auto_clear_date = tempClearDate.value
  showClearDatePicker.value = false
  const date = new Date(tempClearDate.value)
  console.log(`确认账单清零日期: 每年${date.getMonth() + 1}月${date.getDate()}日`)
}

// 取消选择
const cancelDatePicker = () => {
  showClearDatePicker.value = false
}

// 账单清零日期选择事件（保留兼容性）
const onClearDateChange = (event: any) => {
  merchantForm.auto_clear_date = event.value
  const date = new Date(event.value)
  console.log(`选择账单清零日期: 每年${date.getMonth() + 1}月${date.getDate()}日`)
}

// 表单验证规则
const formRules = {
  merchant_name: [
    { required: true, message: '请输入商户名称' },
    {
      min: 2,
      max: 50,
      message: '商户名称长度应在2-50个字符之间',
    },
  ],
  merchant_code: [
    { required: true, message: '请输入商户编码' },
    {
      pattern: /^[A-Z0-9_]{3,20}$/,
      message: '商户编码只能包含大写字母、数字、下划线，长度3-20位',
    },
  ],
  phone: [
    {
      pattern: /^(\d{3,4}-?)?\d{7,11}$/,
      message: '请输入正确的电话号码格式',
    },
  ],
  email: [
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: '邮箱格式不正确',
    },
  ],
  business_license: [
    { required: true, message: '请输入营业执照号' },
  ],
  platform_commission_rate: [
    {
      pattern: /^0\.\d{1,4}$|^1\.0{1,4}$/,
      message: '佣金比例应为0-1之间的小数，如：0.05',
    },
  ],
  status: [{ required: true, message: '请选择商户状态' }],
} as any

// 步骤导航方法
const nextStep = async () => {
  // 验证当前步骤的表单字段
  const stepFields = getStepFields(currentStep.value)
  if (stepFields.length > 0) {
    try {
      // 只验证当前步骤的字段
      const result = await formRef.value.validate(stepFields)
      if (!result.valid) {
        uni.showToast({
          title: '请完善当前步骤信息',
          icon: 'none',
        })
        return
      }
    } catch (error) {
      console.error('步骤验证失败:', error)
      uni.showToast({
        title: '请完善当前步骤信息',
        icon: 'none',
      })
      return
    }
  }

  if (currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 获取每个步骤需要验证的字段
const getStepFields = (step: number): string[] => {
  switch (step) {
    case 0: // 基本信息
      return ['merchant_name', 'merchant_code', 'business_license']
    case 1: // 联系信息
      return ['phone', 'email']
    case 2: // 营业信息
      return ['platform_commission_rate', 'status']
    case 3: // 其他信息
      return []
    default:
      return []
  }
}

// 失去焦点时校验对应字段
const handleMerchantNameBlur = () => {
  if (formRef.value && merchantForm.merchant_name) {
    formRef.value.validate('merchant_name')
  }
}

const handleMerchantCodeBlur = () => {
  if (formRef.value && merchantForm.merchant_code) {
    formRef.value.validate('merchant_code')
  }
}

const handlePhoneBlur = () => {
  if (formRef.value && merchantForm.phone) {
    formRef.value.validate('phone')
  }
}

const handleEmailBlur = () => {
  if (formRef.value && merchantForm.email) {
    formRef.value.validate('email')
  }
}

const handleBusinessLicenseBlur = () => {
  if (formRef.value && merchantForm.business_license) {
    formRef.value.validate('business_license')
  }
}

// 分类选择事件
const onCategoryChange = ({ value }: { value: string }) => {
  merchantForm.category_id = value
}

// 状态选择事件
const onStatusChange = ({ value }: { value: number }) => {
  merchantForm.status = value
}

// 头像上传
const {
  loading: avatarUploadLoading,
  error: avatarUploadError,
  data: avatarUploadData,
  run: startAvatarUpload,
} = useUpload<string>()

// 营业执照上传
const {
  loading: licenseUploadLoading,
  error: licenseUploadError,
  data: licenseUploadData,
  run: startLicenseUpload,
} = useUpload<string>()

// 图片裁剪功能（可选）
const showAvatarCropper = ref(false)
const showLicenseCropper = ref(false)
const tempAvatarSrc = ref('')
const tempLicenseSrc = ref('')

// 监听头像上传结果
watch(
  () => avatarUploadData.value,
  (newData) => {
    if (newData) {
      try {
        // 解析上传返回的数据
        const uploadResult = JSON.parse(newData)

        // 检查返回结果的格式
        if (uploadResult.code === 200 && uploadResult.data && uploadResult.data.file_url) {
          // 使用 file_url 作为头像地址
          merchantForm.avatar = uploadResult.data.file_url

          uni.showToast({
            title: '头像上传成功',
            icon: 'success',
          })
        } else {
          throw new Error(uploadResult.message || '头像上传失败')
        }
      } catch (e) {
        console.error('解析头像上传结果失败:', e)
        uni.showToast({
          title: '头像上传失败',
          icon: 'none',
        })
      }
    }
  },
)

// 监听头像上传错误
watch(
  () => avatarUploadError.value,
  (hasError) => {
    if (hasError) {
      uni.showToast({
        title: '头像上传失败',
        icon: 'none',
      })
    }
  },
)

// 监听营业执照上传结果
watch(
  () => licenseUploadData.value,
  (newData) => {
    if (newData) {
      try {
        // 解析上传返回的数据
        const uploadResult = JSON.parse(newData)

        // 检查返回结果的格式
        if (uploadResult.code === 200 && uploadResult.data && uploadResult.data.file_url) {
          // 使用 file_url 作为营业执照地址
          merchantForm.license_photo = uploadResult.data.file_url

          uni.showToast({
            title: '营业执照上传成功',
            icon: 'success',
          })
        } else {
          throw new Error(uploadResult.message || '营业执照上传失败')
        }
      } catch (e) {
        console.error('解析营业执照上传结果失败:', e)
        uni.showToast({
          title: '营业执照上传失败',
          icon: 'none',
        })
      }
    }
  },
)

// 监听营业执照上传错误
watch(
  () => licenseUploadError.value,
  (hasError) => {
    if (hasError) {
      uni.showToast({
        title: '营业执照上传失败',
        icon: 'none',
      })
    }
  },
)

// 头像上传功能
const chooseAvatar = () => {
  uni.showModal({
    title: '更换头像',
    content: '是否要更换头像？',
    success: (res) => {
      if (res.confirm) {
        // 使用新的参数方式调用商户头像上传
        startAvatarUpload('/sys-merchant/avatar', 'avatar')
      }
    },
  })
}

// 营业执照照片上传
const chooseLicensePhoto = () => {
  uni.showModal({
    title: '上传营业执照',
    content: '是否要上传营业执照照片？',
    success: (res) => {
      if (res.confirm) {
        // 使用新的参数方式调用营业执照上传
        startLicenseUpload('/sys-merchant/license', 'license')
      }
    },
  })
}

// 获取当前位置
const getCurrentLocation = () => {
  uni.getLocation({
    type: 'gcj02', // 返回国测局坐标
    success: (res) => {
      merchantForm.longitude = res.longitude
      merchantForm.latitude = res.latitude

      // 更新地图选点中心点
      pickerMapCenter.value = {
        latitude: res.latitude,
        longitude: res.longitude,
      }

      console.log('获取当前位置成功:', res)
      uni.showToast({
        title: '位置获取成功',
        icon: 'success',
      })
    },
    fail: (error) => {
      console.error('获取位置失败:', error)
      uni.showToast({
        title: '位置获取失败',
        icon: 'none',
      })
    },
  })
}

// 加载商户详情
const loadMerchantDetail = async (merchantId: string) => {
  try {
    uni.showLoading({ title: '加载中...' })

    // 调用真实API获取商户详情
    const result = await getSystemMerchantDetailApi(Number(merchantId))
    merchantDetail.value = result.data

    // 填充表单数据
    Object.assign(merchantForm, {
      merchant_name: result.data.merchant_name || '',
      merchant_code: result.data.merchant_code || '',
      category_id: result.data.category_id || '',
      phone: result.data.phone || '',
      email: result.data.email || '',
      address: result.data.address || '',
      business_license: result.data.business_license || '',
      license_photo: result.data.license_photo || '',
      avatar: result.data.avatar || '',
      description: result.data.description || '',
      platform_commission_rate: result.data.platform_commission_rate || '',
      status: result.data.status || 1,
      auto_clear_date: result.data.auto_clear_date || new Date().getTime(),
      sort_order: result.data.sort_order || 0,
      remark: result.data.remark || '',
      longitude: result.data.location?.longitude || 0,
      latitude: result.data.location?.latitude || 0,
    })

    console.log('商户详情加载成功:', merchantDetail.value)
  } catch (error) {
    console.error('加载商户详情失败:', error)
    uni.showToast({
      title: '加载商户详情失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 格式化日期为API需要的字符串格式
const formatDateForApi = (timestamp: number): string => {
  const date = new Date(timestamp)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${month}-${day}` // MM-DD格式
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 验证表单
    const { valid, errors } = await formRef.value.validate()

    console.log('表单验证结果:', { valid, errors })

    if (!valid) {
      console.log('表单验证失败:', errors)
      uni.showToast({
        title: '请检查表单信息',
        icon: 'none',
      })
      return
    }

    console.log('表单验证通过，开始提交数据')
    submitting.value = true

    // 准备提交数据
    const submitData: ISysMerchantCreateRequest = {
      merchant_name: merchantForm.merchant_name,
      merchant_code: merchantForm.merchant_code,
      category_id: merchantForm.category_id || null,
      phone: merchantForm.phone || null,
      email: merchantForm.email || null,
      address: merchantForm.address || null,
      location:
        merchantForm.longitude && merchantForm.latitude
          ? {
              longitude: merchantForm.longitude,
              latitude: merchantForm.latitude,
            }
          : null,
      business_license: merchantForm.business_license || null,
      license_photo: merchantForm.license_photo || null,
      avatar: merchantForm.avatar || null,
      description: merchantForm.description || null,
      platform_commission_rate: merchantForm.platform_commission_rate || null,
      status: merchantForm.status,
      auto_clear_date: merchantForm.auto_clear_date
        ? formatDateForApi(merchantForm.auto_clear_date)
        : null,
      sort_order: merchantForm.sort_order || null,
      remark: merchantForm.remark || null,
    }

    if (isEditMode.value) {
      // 编辑模式 - 调用更新API
      console.log('编辑提交数据:', submitData)
      await updateSystemMerchantApi(
        Number(pageParams.value.merchantId),
        submitData as ISysMerchantUpdateRequest,
      )

      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      // 新增模式 - 调用创建API
      console.log('新增提交数据:', submitData)
      await createSystemMerchantApi(submitData)

      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1000)
  } catch (error) {
    console.error('操作失败:', error)

    if (error && typeof error === 'object' && 'errors' in error) {
      console.log('表单验证异常:', error)
      uni.showToast({
        title: '表单验证失败',
        icon: 'none',
      })
    } else {
      uni.showToast({
        title: isEditMode.value ? '更新失败' : '创建失败',
        icon: 'none',
      })
    }
  } finally {
    submitting.value = false
  }
}

// 保存当前步骤
const saveCurrentStep = async () => {
  const stepFields = getStepFields(currentStep.value)
  if (stepFields.length > 0) {
    try {
      const result = await formRef.value.validate(stepFields)
      if (result.valid) {
        submitting.value = true
        const submitData: ISysMerchantUpdateRequest = {
          merchant_name: merchantForm.merchant_name,
          merchant_code: merchantForm.merchant_code,
          category_id: merchantForm.category_id || null,
          phone: merchantForm.phone || null,
          email: merchantForm.email || null,
          address: merchantForm.address || null,
          location:
            merchantForm.longitude && merchantForm.latitude
              ? {
                  longitude: merchantForm.longitude,
                  latitude: merchantForm.latitude,
                }
              : null,
          business_license: merchantForm.business_license || null,
          license_photo: merchantForm.license_photo || null,
          avatar: merchantForm.avatar || null,
          description: merchantForm.description || null,
          platform_commission_rate: merchantForm.platform_commission_rate || null,
          status: merchantForm.status,
          auto_clear_date: merchantForm.auto_clear_date
            ? formatDateForApi(merchantForm.auto_clear_date)
            : null,
          sort_order: merchantForm.sort_order || null,
          remark: merchantForm.remark || null,
        }

        if (isEditMode.value) {
          await updateSystemMerchantApi(Number(pageParams.value.merchantId), submitData)
          uni.showToast({
            title: '保存成功',
            icon: 'success',
          })
          
          // 保存成功后返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1000)
        }
      } else {
        uni.showToast({
          title: '请完善当前步骤信息',
          icon: 'none',
        })
      }
    } catch (error) {
      console.error('保存步骤失败:', error)
      uni.showToast({
        title: '保存失败',
        icon: 'none',
      })
    } finally {
      submitting.value = false
    }
  }
}

// 取消操作
const handleCancel = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消操作吗？未保存的内容将丢失。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    },
  })
}

// 返回功能
const handleBack = () => {
  handleCancel()
}

// 页面加载事件
onLoad((options) => {
  console.log('商户表单页面参数:', options)

  if (options) {
    pageParams.value.mode = (options.mode as 'add' | 'edit') || 'add'
    pageParams.value.merchantId = options.merchantId
  }

  // 如果是编辑模式，加载商户详情
  if (isEditMode.value && pageParams.value.merchantId) {
    loadMerchantDetail(pageParams.value.merchantId)
  }
})

onMounted(() => {
  console.log('商户表单页面挂载完成')
  console.log('页面模式:', pageParams.value.mode)
  console.log('商户ID:', pageParams.value.merchantId)
  fetchCategoryOptions()
})

// 地图相关数据
const mapSupported = ref(true) // 地图是否支持
const showMapPicker = ref(false) // 地图选点弹出层
const selectedLocation = ref({ latitude: 0, longitude: 0 }) // 选中的位置
const selectedLocationAddress = ref('') // 选中位置的地址信息
const pickerMapCenter = ref({ latitude: 39.908692, longitude: 116.397477 }) // 地图选点中心点（默认北京）

// 地图标记点
const mapMarkers = computed(() => {
  if (!merchantForm.longitude || !merchantForm.latitude) return []
  return [
    {
      id: 1,
      latitude: merchantForm.latitude,
      longitude: merchantForm.longitude,
      title: merchantForm.merchant_name || '商户位置',
      iconPath: '', // 使用默认图标
      width: 30,
      height: 30,
    },
  ]
})

// 地图选点标记
const pickerMarkers = computed(() => {
  if (!selectedLocation.value.latitude) return []
  return [
    {
      id: 1,
      latitude: selectedLocation.value.latitude,
      longitude: selectedLocation.value.longitude,
      iconPath: '', // 使用默认图标
      width: 30,
      height: 30,
    },
  ]
})

// 打开地图选点
const openMapPicker = () => {
  // 如果已有位置，以当前位置为中心
  if (merchantForm.longitude && merchantForm.latitude) {
    pickerMapCenter.value = {
      latitude: merchantForm.latitude,
      longitude: merchantForm.longitude,
    }
    selectedLocation.value = {
      latitude: merchantForm.latitude,
      longitude: merchantForm.longitude,
    }
  }
  showMapPicker.value = true
}

// 地图点击事件 - 直接选定位置
const onMapTap = (e: any) => {
  if (e.detail && e.detail.latitude && e.detail.longitude) {
    selectedLocation.value = {
      latitude: e.detail.latitude,
      longitude: e.detail.longitude,
    }

    console.log('地图点击位置:', e.detail)

    // 获取地址信息
    getAddressFromLocation(e.detail.latitude, e.detail.longitude)
  }
}

// POI点击事件 - 处理地图上的兴趣点点击
const onPoiTap = (e: any) => {
  console.log('POI点击事件:', e)

  if (e.detail) {
    const { latitude, longitude, name, address } = e.detail

    if (latitude && longitude) {
      selectedLocation.value = {
        latitude: latitude,
        longitude: longitude,
      }

      // 显示选择成功提示，包含POI名称
      uni.showToast({
        title: name ? `已选择: ${name}` : '位置已选择',
        icon: 'success',
        duration: 1500,
      })

      // 总是调用API获取详细地址信息，不直接使用POI的简单信息
      console.log('选择了POI:', name, '坐标:', { latitude, longitude })
      console.log('正在获取详细地址信息...')
      getAddressFromLocation(latitude, longitude)
    }
  }
}

// 根据坐标获取地址信息（逆地理编码）
const getAddressFromLocation = (latitude: number, longitude: number) => {
  selectedLocationAddress.value = '正在获取地址信息...'

  // 使用uni.request调用地理编码API（这里使用腾讯地图API示例）
  uni.request({
    url: 'https://apis.map.qq.com/ws/geocoder/v1/',
    data: {
      location: `${latitude},${longitude}`,
      key: 'B4MBZ-SBWC3-NK53Y-ONHJP-KES25-VCFVU', // 需要替换为实际的API密钥
      get_poi: 1, // 获取POI信息
      poi_options: 'policy=2;radius=1000', // 搜索半径1000米，返回更多POI
      extensions: 'all', // 返回所有扩展信息
    },
    success: (res: any) => {
      console.log('地址反查API完整响应:', res)

      if (res.data && res.data.status === 0 && res.data.result) {
        const result = res.data.result

        // 构建详细地址信息
        let detailedAddress = ''

        // 基础地址（包含省市区街道等）
        if (result.address) {
          detailedAddress = result.address
        }

        // 如果有地址组件，构建更详细的地址
        if (result.address_component) {
          const component = result.address_component
          const addressParts = []

          if (component.province) addressParts.push(component.province)
          if (component.city) addressParts.push(component.city)
          if (component.district) addressParts.push(component.district)
          if (component.street) addressParts.push(component.street)
          if (component.street_number) addressParts.push(component.street_number)

          if (addressParts.length > 0) {
            detailedAddress = addressParts.join('')
          }
        }

        // 如果有格式化地址，优先使用
        if (result.formatted_addresses && result.formatted_addresses.standard_address) {
          detailedAddress = result.formatted_addresses.standard_address
        }

        selectedLocationAddress.value = detailedAddress || '已获取位置'
        console.log('获取到详细地址:', detailedAddress)
      } else {
        selectedLocationAddress.value = '无法获取地址信息'
        console.log('地址反查失败:', res.data)
      }
    },
    fail: () => {
      // API调用失败时使用备用方案
      selectedLocationAddress.value = `位置: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
      console.log('地址反查API调用失败')
    },
  })
}

// 确认地图选点
const confirmMapPicker = () => {
  if (selectedLocation.value.latitude && selectedLocation.value.longitude) {
    // 更新表单中的坐标
    merchantForm.longitude = selectedLocation.value.longitude
    merchantForm.latitude = selectedLocation.value.latitude

    // 如果获取到了地址信息，也更新到表单的地址字段
    if (
      selectedLocationAddress.value &&
      !selectedLocationAddress.value.includes('正在获取') &&
      !selectedLocationAddress.value.includes('无法获取')
    ) {
      merchantForm.address = selectedLocationAddress.value
    }

    showMapPicker.value = false

    uni.showToast({
      title: '位置设置成功',
      icon: 'success',
    })

    console.log('位置已设置:', {
      longitude: merchantForm.longitude,
      latitude: merchantForm.latitude,
      address: merchantForm.address,
    })
  }
}

// 标记点击事件
const onMarkerTap = (e: any) => {
  console.log('标记点击:', e)
}

// 选点标记点击事件
const onPickerMarkerTap = (e: any) => {
  console.log('选点标记点击:', e)
}

// 取消地图选点
const cancelMapPicker = () => {
  showMapPicker.value = false
  selectedLocation.value = { latitude: 0, longitude: 0 }
}

// 文本域自适应高度处理
const handleTextareaInput = () => {
  // 在下一个tick中强制重新计算高度
  nextTick(() => {
    console.log('文本域内容变化，触发高度重计算')
  })
}

// 监听文本域内容变化
const onAddressInput = () => {
  handleTextareaInput()
}

const onDescriptionInput = () => {
  handleTextareaInput()
}

const onRemarkInput = () => {
  handleTextareaInput()
}

// 获取商户分类选项
const fetchCategoryOptions = async () => {
  try {
    const result = await getSystemMerchantCategorySelectItemsApi()

    // 设置分类选项
    categoryOptions.value = result.data.map((item) => ({
      label: item.category_name,
      value: item.id,
    }))

    console.log('获取商户分类选项成功:', categoryOptions.value)
  } catch (error) {
    console.error('获取商户分类选项失败:', error)
    uni.showToast({
      title: '获取分类数据失败',
      icon: 'none',
    })
  }
}

// 图片加载处理函数
const handleAvatarLoad = (event: any) => {
  console.log('头像加载成功:', event)
}

const handleAvatarError = (event: any) => {
  console.error('头像加载失败:', event)
  uni.showToast({
    title: '头像加载失败',
    icon: 'none',
  })
}

const handleLicenseLoad = (event: any) => {
  console.log('营业执照加载成功:', event)
}

const handleLicenseError = (event: any) => {
  console.error('营业执照加载失败:', event)
  uni.showToast({
    title: '营业执照加载失败',
    icon: 'none',
  })
}

// 头像裁剪处理方法
const handleAvatarCropConfirm = (result: any) => {
  console.log('头像裁剪确认:', result)
  if (result && result.tempFilePath) {
    merchantForm.avatar = result.tempFilePath
  }
  showAvatarCropper.value = false
}

const handleAvatarCropCancel = () => {
  console.log('头像裁剪取消')
  showAvatarCropper.value = false
  tempAvatarSrc.value = ''
}

// 营业执照裁剪处理方法
const handleLicenseCropConfirm = (result: any) => {
  console.log('营业执照裁剪确认:', result)
  if (result && result.tempFilePath) {
    merchantForm.license_photo = result.tempFilePath
  }
  showLicenseCropper.value = false
}

const handleLicenseCropCancel = () => {
  console.log('营业执照裁剪取消')
  showLicenseCropper.value = false
  tempLicenseSrc.value = ''
}
</script>

<style lang="scss" scoped>
.merchant-form-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.main-layout {
  padding: 12px;
}

// 步骤条样式
.steps-wrapper {
  background-color: white;
  margin-bottom: 12px;
  padding: 20px 12px;
  border-radius: 8px;
}

:deep(.wd-steps) {
  .wd-step__title {
    font-size: 12px;
    margin-top: 6px;
  }
}

.merchant-form {
  // 简化表单样式，直接展示
}

.form-section {
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 15px 12px 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin: 15px 0 8px 0;
  padding-left: 15px;
}

// 头像区域样式
.avatar-section {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: white;
  margin-bottom: 12px;
  border-radius: 8px;
}

.avatar-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  width: 100px;
  text-align: right;
  padding-right: 12px;
}

.avatar-upload {
  flex: 1;
}

.avatar-container,
.avatar-view {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  border: 2px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: border-color 0.3s;

  &:hover {
    border-color: #4285f4;
  }
}

.avatar-view {
  border-style: solid;
  border-color: #eee;
}

// wd-img 组件样式重写
:deep(.avatar-image) {
  border-radius: 8px;
  border: none !important;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.placeholder-text {
  font-size: 12px;
  color: #999;
  text-align: center;
}

// 营业执照区域样式
.license-section {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background-color: white;
  margin-bottom: 12px;
  border-radius: 8px;
}

.license-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  width: 100px;
  text-align: right;
  padding-right: 12px;
  margin-top: 10px;
}

.license-upload {
  flex: 1;
}

.license-container,
.license-view {
  position: relative;
  width: 160px;
  height: 120px;
  border-radius: 8px;
  border: 2px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: border-color 0.3s;

  &:hover {
    border-color: #4285f4;
  }
}

.license-view {
  border-style: solid;
  border-color: #eee;
}

// wd-img 组件样式重写
:deep(.license-image) {
  border-radius: 8px;
  border: none !important;
}

// 位置操作按钮
.location-section {
  margin-top: 15px;
}

.location-actions {
  padding: 10px;
  display: flex;
  justify-content: center;
  gap: 6px;
}

// 系统信息区域
.system-info-section {
  margin-top: 15px;
}

// 清零日期显示样式
.clear-date-display {
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: flex-start;
}

.date-text {
  font-size: 14px;
  color: #333;
}

// Cell组件右侧内容靠左对齐
:deep(.wd-cell .wd-cell__body) {
  .wd-cell__value {
    text-align: left !important;
    justify-content: flex-start !important;

    // 针对我们自定义的内容区域
    > view {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
}

// 日期选择弹出层样式
.date-picker-popup {
  background-color: white;
  padding: 0 0 env(safe-area-inset-bottom) 0;
}

.popup-header {
  padding: 20px 20px 10px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 6px;
}

.popup-subtitle {
  font-size: 12px;
  color: #999;
  display: block;
}

.popup-tips {
  font-size: 12px;
  color: #999;
  display: block;
  margin-top: 6px;
}

.picker-container {
  padding: 0 20px;
}

.popup-actions {
  display: flex;
  gap: 12px;
  padding: 20px;
  justify-content: center;
  align-items: center;
}

.popup-actions .wd-button {
  flex: 1;
  max-width: 140px;
}

// 步骤导航按钮
.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  padding: 12px;
}

.view-actions {
  padding: 12px;
}

:deep(.prev-btn) {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666 !important;
  border-color: #ddd !important;
}

:deep(.save-btn) {
  flex: 1;
  background-color: #52c41a !important;
  border-color: #52c41a !important;
}

:deep(.next-btn) {
  flex: 1;
}

:deep(.submit-btn) {
  flex: 1;
}

:deep(.edit-btn) {
  width: 100%;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

:deep(.wd-input) {
  background-color: transparent;
}

:deep(.wd-picker) {
  background-color: transparent;
}

// 在Cell组件中的文本域样式重置
:deep(.wd-cell) {
  .wd-textarea {
    background-color: transparent !important;

    .wd-textarea__inner {
      height: auto !important;      // 允许元素自由增长
      overflow-y: hidden !important; // 隐藏滚动条，由组件逻辑动态调整
    }
  }

  .wd-cell__value {
    height: auto !important;
    min-height: auto !important;
    align-items: flex-start !important;
  }
}

// 排序权重容器样式
.sort-order-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

// 地图组件直接样式
.merchant-location-map {
  width: 100%;
  height: 300px !important;
  min-height: 200px;
  margin: 12px 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
}

.location-info {
  padding: 12px 15px;
  background-color: #f8f9fa;
  border: 1px solid #eee;
  border-top: none;
  border-radius: 0 0 8px 8px;
  margin-bottom: 12px;
}

.location-text {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.address-text {
  display: block;
  font-size: 12px;
  color: #333;
}

// 地图选点弹出层样式
.map-picker-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  overflow: hidden;
}

.map-picker-container {
  flex: 1;
  position: relative;
  height: 300px; // 固定地图高度，不占满整个弹层
  min-height: 300px;
  max-height: 300px;
  overflow: hidden;
}

.picker-map {
  width: 100%;
  height: 100%;
  border: none;
}

.selected-location-info {
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #eee;
  flex-shrink: 0; // 防止被压缩
}

.selected-address {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
}

// 上传状态指示器样式
.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-text {
  font-size: 12px;
  color: white;
}

// Textarea auto-height 适配
:deep(.wd-textarea) {
  .wd-textarea__inner[auto-height] {
    height: auto !important;      // 允许元素自由增长
    overflow-y: hidden !important; // 隐藏滚动条，由组件逻辑动态调整
  }
}

// 步骤保存按钮样式
.step-save-section {
  margin-top: 15px;
  padding: 12px;
  background-color: #f8f9fa;
  border: 1px solid #eee;
  border-radius: 8px;
}

.step-save-btn {
  width: 100%;
}
</style>
 