<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '商户详情',
  },
  access: {
    requireAuth: true,
  },
}
</route>

<template>
  <view class="merchant-detail-container">
    <!-- 使用Wot UI官方导航栏组件 -->
    <wd-navbar
      :title="merchantDetail?.merchant_name"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="handleBack"
      custom-class="custom-navbar"
    />

    <view class="page-content">
      <!-- 商户基础信息卡片 -->
      <view class="merchant-basic-info">
        <view class="merchant-header">
          <view class="merchant-avatar">
            <wd-img
              v-if="merchantDetail?.avatar"
              :src="merchantDetail.avatar"
              width="60px"
              height="60px"
              round
              mode="aspectFill"
              :enable-preview="true"
              @error="handleAvatarError"
              @load="handleAvatarLoad"
            />
            <view v-else class="avatar-placeholder">
              <text class="iconfont-sys iconsys-dianpu" />
            </view>
          </view>
          <view class="merchant-info">
            <view class="merchant-name">{{ merchantDetail?.merchant_name || '-' }}</view>
            <view class="merchant-meta">
              <text class="merchant-code">{{ merchantDetail?.merchant_code || '-' }}</text>
              <text class="merchant-category">{{ merchantDetail?.category_name || '-' }}</text>
            </view>
          </view>
          <view class="merchant-status" :class="'status-' + merchantDetail?.status">
            {{ getStatusLabel(merchantDetail?.status) }}
          </view>
        </view>
        <view class="merchant-contact">
          <view class="contact-item">
            <text class="iconfont-sys iconsys-dianhua" />
            <text class="contact-text">{{ merchantDetail?.phone || '-' }}</text>
          </view>
          <view class="contact-item">
            <text class="iconfont-sys iconsys-weizhi" />
            <text class="contact-text">{{ merchantDetail?.address || '-' }}</text>
          </view>
        </view>
      </view>

      <!-- 统计卡片区域 -->
      <view class="stats-cards">
        <view class="stats-row">
          <!-- 年营业额卡片 -->
          <view class="stats-card">
            <view class="card-title">年营业额</view>
            <view class="card-value">
              <wd-count-to
                prefix="¥"
                :start-val="0"
                :end-val="1800000"
                :duration="1000"
                :decimals="2"
                separator=","
                autoplay
                :color="'#000000'"
              />
            </view>
            <view class="card-icon">
              <text class="iconfont-sys iconsys-tongji" />
            </view>
          </view>

          <!-- 月营业额卡片 -->
          <view class="stats-card">
            <view class="card-title">月营业额</view>
            <view class="card-value">
              <wd-count-to
                prefix="¥"
                :start-val="0"
                :end-val="150000"
                :duration="1000"
                :decimals="2"
                separator=","
                autoplay
                :color="'#000000'"
              />
            </view>
            <view class="card-icon">
              <text class="iconfont-sys iconsys-qushi" />
            </view>
          </view>
        </view>

        <view class="stats-row">
          <!-- 平台抽成卡片 -->
          <view class="stats-card">
            <view class="card-title">平台抽成</view>
            <view class="card-value">
              <wd-count-to
                prefix="¥"
                :start-val="0"
                :end-val="54000"
                :duration="1000"
                :decimals="2"
                separator=","
                autoplay
                :color="'#000000'"
              />
            </view>
            <view class="card-icon">
              <text class="iconfont-sys iconsys-qianbao" />
            </view>
          </view>

          <!-- 佣金比例卡片 -->
          <view class="stats-card">
            <view class="card-title">佣金比例</view>
            <view class="card-value">
              <view class="commission-row">
                <text class="commission-label">平台：</text>
                <wd-count-to
                  :start-val="0"
                  :end-val="3.5"
                  :duration="1000"
                  :decimals="1"
                  suffix="%"
                  autoplay
                  :color="'#000000'"
                />
              </view>
              <view class="commission-row">
                <text class="commission-label">跟进人：</text>
                <wd-count-to
                  :start-val="0"
                  :end-val="1.5"
                  :duration="1000"
                  :decimals="1"
                  suffix="%"
                  autoplay
                  :color="'#000000'"
                />
              </view>
            </view>
            <view class="card-icon">
              <text class="iconfont-sys iconsys-shouyi" />
            </view>
          </view>
        </view>
      </view>

      <!-- 其他详细信息 -->
      <view class="main-layout">
        <view class="merchant-detail" v-if="merchantDetail">
          <view class="detail-section">
            <view class="section-title">商户详情</view>
            
            <wd-cell-group border>
              <!-- 营业执照号 -->
              <wd-cell title="营业执照号" :value="merchantDetail.business_license || '-'" />

              <!-- 经营有效期 -->
              <wd-cell title="经营有效期" value="2024-01-01 至 2029-12-31" />

              <!-- 邮箱地址 -->
              <wd-cell title="邮箱地址" :value="merchantDetail.email || '-'" />

              <!-- 地理位置 -->
              <wd-cell title="地理位置" :value="merchantDetail.address || '-'" />

              <!-- 商户描述 -->
              <wd-cell title="商户描述" :value="merchantDetail.description || '-'" />

              <!-- 备注 -->
              <wd-cell title="备注" :value="merchantDetail.remark || '-'" />
            </wd-cell-group>

            <!-- 营业执照照片 -->
            <view v-if="merchantDetail.license_photo" class="license-wrapper">
              <view class="license-title">营业执照照片</view>
              <view class="license-display-area has-image">
                <wd-img
                  :src="merchantDetail.license_photo"
                  width="100%"
                  height="200px"
                  mode="aspectFit"
                  custom-class="license-image"
                  :enable-preview="true"
                  @error="handleLicenseError"
                  @load="handleLicenseLoad"
                  @click="previewLicense"
                />
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons">
            <wd-button
              type="primary"
              @click="switchToEditMode"
              size="large"
              custom-class="edit-btn"
            >
              编辑商户
            </wd-button>
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-else class="loading-container">
          <wd-loading />
          <text class="loading-text">加载中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getSystemMerchantDetailApi } from '@/api/sys/systemMerchantApi'
import type { ISysMerchantDetailResponse } from '@/api/sys/types/merchant'

defineOptions({
  name: 'MerchantDetail',
})

// 页面参数
const pageParams = ref<{
  merchantId?: string
}>({})

// 商户详情数据
const merchantDetail = ref<ISysMerchantDetailResponse | null>(null)

// 商户分类选项（与表单页面保持一致）
const categoryOptions = [
  { label: '餐饮', value: '550e8400-e29b-41d4-a716-446655440000' },
  { label: '零售', value: '550e8400-e29b-41d4-a716-446655440001' },
  { label: '服务', value: '550e8400-e29b-41d4-a716-446655440002' },
  { label: '娱乐', value: '550e8400-e29b-41d4-a716-446655440003' },
]

// 状态选项
const statusOptions = [
  { label: '正常营业', value: 1 },
  { label: '临时关闭', value: 2 },
  { label: '永久关闭', value: 3 },
]

// 地图标记点
const mapMarkers = computed(() => {
  if (!merchantDetail.value?.location?.longitude || !merchantDetail.value?.location?.latitude) {
    return []
  }
  return [
    {
      id: 1,
      latitude: merchantDetail.value.location.latitude,
      longitude: merchantDetail.value.location.longitude,
      title: merchantDetail.value.merchant_name || '商户位置',
      iconPath: '',
      width: 30,
      height: 30,
    },
  ]
})

// 获取状态标签
const getStatusLabel = (status: number | null): string => {
  if (status === null || status === undefined) return '-'
  const statusItem = statusOptions.find((item) => item.value === status)
  return statusItem?.label || '-'
}

// 获取清零日期显示文本
const getClearDateText = (clearDate: string | number | null): string => {
  if (!clearDate) return '-'
  try {
    // 如果是数字类型（时间戳）
    if (typeof clearDate === 'number') {
      const date = new Date(clearDate)
      return `每年${date.getMonth() + 1}月${date.getDate()}日`
    }

    // 如果是字符串类型
    const clearDateStr = String(clearDate)

    // 如果是MM-DD格式
    if (clearDateStr.includes('-') && clearDateStr.length <= 5) {
      const [month, day] = clearDateStr.split('-')
      return `每年${month}月${day}日`
    }

    // 尝试解析为日期
    const date = new Date(clearDateStr)
    if (!isNaN(date.getTime())) {
      return `每年${date.getMonth() + 1}月${date.getDate()}日`
    }

    return clearDateStr
  } catch (error) {
    console.error('日期格式转换错误:', error)
    return String(clearDate)
  }
}

// 地图标记点击事件
const onMarkerTap = (e: any) => {
  console.log('标记点击:', e)
}

// 加载商户详情
const loadMerchantDetail = async (merchantId: string) => {
  try {
    uni.showLoading({ title: '加载中...' })

    const result = await getSystemMerchantDetailApi(Number(merchantId))
    merchantDetail.value = result.data

    console.log('商户详情加载成功:', merchantDetail.value)
  } catch (error) {
    console.error('加载商户详情失败:', error)
    uni.showToast({
      title: '加载商户详情失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 切换到编辑模式
const switchToEditMode = () => {
  const editUrl = `/pages-sys/merchant/merchant/merchant-form?mode=edit&merchantId=${pageParams.value.merchantId}`
  uni.navigateTo({
    url: editUrl,
  })
}

// 返回功能
const handleBack = () => {
  uni.navigateBack()
}

// 页面加载事件
onLoad((options) => {
  console.log('商户详情页面参数:', options)

  if (options && options.merchantId) {
    pageParams.value.merchantId = options.merchantId
    loadMerchantDetail(options.merchantId)
  } else {
    uni.showToast({
      title: '缺少商户ID参数',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

onMounted(() => {
  console.log('商户详情页面挂载完成')
})

// 图片处理函数
const handleAvatarLoad = (event: any) => {
  console.log('头像加载成功:', event)
}

const handleAvatarError = (event: any) => {
  console.error('头像加载失败:', event)
  uni.showToast({
    title: '头像加载失败',
    icon: 'none',
  })
}

const handleLicenseLoad = (event: any) => {
  console.log('营业执照加载成功:', event)
}

const handleLicenseError = (event: any) => {
  console.error('营业执照加载失败:', event)
  uni.showToast({
    title: '营业执照加载失败',
    icon: 'none',
  })
}

// 预览营业执照（额外的点击处理，wd-img已内置预览功能）
const previewLicense = () => {
  if (merchantDetail.value?.license_photo) {
    console.log('点击预览营业执照:', merchantDetail.value.license_photo)
    // wd-img 组件的 enable-preview 已经处理预览功能
    // 这里可以添加额外的统计或日志记录
  }
}
</script>

<style lang="scss" scoped>
.merchant-detail-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  position: relative;
}

// 自定义导航栏样式
:deep(.custom-navbar) {
  background-color: #4285f4 !important;
}

:deep(.custom-navbar .wd-navbar__title) {
  color: white !important;
  font-size: 20px !important;
  font-weight: bold !important;
}

:deep(.custom-navbar .wd-navbar__left) {
  margin-left: 8px !important;

  .wd-button {
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    min-width: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    background-color: transparent !important;
  }

  .wd-icon {
    font-size: 26px !important;
    color: white !important;
    margin: 0 !important;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.main-layout {
  flex: 1;
  padding: 0 12px 12px;
}

.merchant-detail {
  // 简化详情样式，直接展示
}

.detail-section {
  margin-bottom: 12px;
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e5e5;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e5e5;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin: 15px 0 8px 0;
  padding-left: 15px;
}

// 头像区域样式
.avatar-section {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: white;
  margin-bottom: 12px;
  border-radius: 8px;
}

.avatar-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  width: 100px;
  text-align: right;
  padding-right: 12px;
}

.avatar-view {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

// 营业执照区域样式
.license-section {
  padding: 16px;
  background-color: #ffffff;
}

.license-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 12px;
  text-align: center;
}

.license-display-area {
  width: 100%;
  min-height: 200px;
  border-radius: 8px;
  border: 1px solid #e5e5e5;
  overflow: hidden;
  
  &:hover {
    border-color: #d0d0d0;
  }
}

// wd-img 组件样式重写
:deep(.license-image) {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  border: none !important;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.01);
  }
}

.license-placeholder-full {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  height: 200px;
}

.placeholder-text {
  font-size: 14px;
  color: #999;
  text-align: center;
}

.placeholder-subtext {
  font-size: 12px;
  color: #ccc;
  text-align: center;
}

// 位置相关样式
.location-section {
  margin-top: 15px;
}

// 地图组件直接样式
.merchant-location-map {
  width: 100%;
  min-height: 300px;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
}

.location-info {
  padding: 12px 15px;
  background-color: #f8f9fa;
  border: 1px solid #eee;
  border-top: none;
  border-radius: 0 0 8px 8px;
  margin-bottom: 12px;
}

.location-text {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.address-text {
  display: block;
  font-size: 12px;
  color: #333;
}

// 操作按钮
.action-buttons {
  padding: 16px;
  margin-top: 20px;
  border-top: 1px solid #e5e5e5;
}

:deep(.edit-btn) {
  width: 100%;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  margin: 12px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

// Wot UI 表单项样式优化
:deep(.wd-cell-group) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.wd-cell) {
  background-color: white;
}

// Cell组件右侧内容靠左对齐
:deep(.wd-cell .wd-cell__body) {
  .wd-cell__value {
    text-align: left !important;
    justify-content: flex-start !important;
  }
}

// 统计卡片样式
.stats-cards {
  padding: 0 12px 12px;
}

.stats-row {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.stats-card {
  flex: 1;
  background-color: #ffffff;
  border-radius: 12px;
  padding: 16px;
  position: relative;
  border: 1px solid #e5e5e5;
  overflow: hidden;

  &:hover {
    border-color: #d0d0d0;
  }
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.card-value {
  font-size: 20px;
  color: #333;
  font-weight: 600;
  line-height: 1.2;
}

.card-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  opacity: 0.8;

  .iconfont-sys {
    font-size: 24px;
    color: #4285f4;

    &.iconsys-tongji {
      color: #4285f4;
    }

    &.iconsys-qushi {
      color: #34a853;
    }

    &.iconsys-qianbao {
      color: #fbbc05;
    }

    &.iconsys-shouyi {
      color: #ea4335;
    }
  }
}

.commission-row {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 1.5;
}

.commission-label {
  color: #666;
  font-size: 14px;
  margin-right: 4px;
}

.commission-value {
  color: #333;
  font-weight: 600;
}

// 商户基础信息卡片样式
.merchant-basic-info {
  margin: 12px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e5e5;
}

.merchant-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.merchant-avatar {
  width: 60px;
  height: 60px;
  margin-right: 16px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;

  .iconsys-dianpu {
    font-size: 32px;
    color: #ccc;
  }
}

.merchant-info {
  flex: 1;
  min-width: 0;
}

.merchant-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.merchant-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.merchant-code {
  font-size: 13px;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.merchant-category {
  font-size: 13px;
  color: #666;
  background-color: #e3f2fd;
  padding: 2px 8px;
  border-radius: 4px;
}

.merchant-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;

  &.status-1 {
    background-color: #e8f5e8;
    color: #4caf50;
  }

  &.status-2 {
    background-color: #fff3e0;
    color: #ff9800;
  }

  &.status-3 {
    background-color: #ffebee;
    color: #f44336;
  }
}

.merchant-contact {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;

  .iconfont-sys {
    font-size: 16px;
    color: #666;
  }

  .contact-text {
    font-size: 14px;
    color: #666;
    flex: 1;
  }
}

// 移除地图相关样式
.merchant-location-map,
.location-section,
.section-subtitle,
.location-info {
  display: none;
}

.license-wrapper {
  padding: 16px;
  border-top: 1px solid #e5e5e5;
}

.license-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}
</style>
